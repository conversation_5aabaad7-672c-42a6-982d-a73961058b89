<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui">
    <ui:style>

    </ui:style>

    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages"/>

    <g:VerticalPanel width="100%" spacing="5">

        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUpTransPanel" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpTransPanel">
                <g:header>
                    <ui:text from="{msg.getUsagePointTxnHistory}"/>
                </g:header>
                <g:VerticalPanel ui:field="upTransPanelVP" styleName="container" width="100%" height="550px"/>
            </g:DisclosurePanel>
        </g:Cell>
        
        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUsagePointHistory" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUsagePointHistory">
                <g:header>
                    <ui:text from="{msg.getUsagePointHistory}"/>
                </g:header>
                <g:VerticalPanel ui:field="usagePointHistoryVP" styleName="container" spacing="20"/>
            </g:DisclosurePanel>
        </g:Cell>
        
        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUpReportsSpecific" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpReportsSpecific">
                <g:header>
                    <ui:text from="{msg.getUsagePointReports}"/>
                </g:header>
                <g:VerticalPanel ui:field="upReportsSpecificVP" styleName="container" width="100%"/>
            </g:DisclosurePanel>
        </g:Cell>
        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUpMeterReadingsPanel" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpMeterReadingsPanel">
                <g:header>
                    <ui:text from="{msg.getUsagePointMeterReadings}"/>
                </g:header>
                <g:VerticalPanel ui:field="upMeterReadingsVP" styleName="container" width="100%" height="600px" debugId="upMeterReadingsVP"/>
            </g:DisclosurePanel>
        </g:Cell>
        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUpRegisterReadingPanel" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpRegisterReadingPanel">
                <g:header>
                    <ui:text from="{msg.getRegisterReadingTxnLabel}"/>
                </g:header>
                <g:VerticalPanel ui:field="upRegisterReadingVP" styleName="container" width="100%" debugId="upRegisterReadingVP"/>
            </g:DisclosurePanel>
        </g:Cell>
        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrMdcTransPanel" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpMdcTransPanel">
                <g:header>
                    <ui:text from="{msg.getMdcTxnLabel}"/>
                </g:header>
                <g:VerticalPanel ui:field="mdcTransPanelVP" styleName="container"/>
            </g:DisclosurePanel>
        </g:Cell>

        <g:Cell width="100%">
            <g:DisclosurePanel ui:field="dsclsrUpUnitsAccountTransactionHistory" width="100%" styleName="gwt-DisclosurePanel-information" debugId="dsclsrUpUnitsAccountTransactionHistory">
                <g:header>
                    <ui:text from="{msg.getUnitsAccountTransactionHistory}"/>
                </g:header>
                <g:VerticalPanel ui:field="unitsAccountTransactionHistoryVP" styleName="container" width="100%"/>
            </g:DisclosurePanel>
        </g:Cell>
    </g:VerticalPanel>
</ui:UiBinder>