package za.co.ipay.metermng.client.view.component.usagepoint;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;

public class UpPricingStructureHistoryView extends BaseComponent implements IpayDataProviderFilter<UpPricingStructureHistData> {

    private enum ColumnsE {
        DATE_MODIFIED("usagepoint.hist.datemod"), USER("usagepoint.hist.user"), ACTION("usagepoint.hist.action"),
        PRICING_STRUCTURE("usagepoint.hist.pricing.structure"), START_DATE("usagepoint.hist.pricing.start"), 
        END_DATE("usagepoint.hist.pricing.end"), CHANGE_REASON("usagepoint.hist.ps.change.reason");

        String key;

        private ColumnsE(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    };

    private static final int DEFAULT_PAGE_SIZE = 15;

    interface UpPricingStructureHistoryWidgetUiBinder extends UiBinder<Widget, UpPricingStructureHistoryView> {
    }

    private static UpPricingStructureHistoryWidgetUiBinder uiBinder = GWT.create(UpPricingStructureHistoryWidgetUiBinder.class);

    @UiField(provided=true) CellTable<UpPricingStructureHistData> clltblHistory;
    @UiField TablePager smplpgrHistory;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    
    private IPayDataProvider<UpPricingStructureHistData> dataProvider;
    private ArrayList<UpPricingStructureHistData> thehistorydata;
    private ListHandler<UpPricingStructureHistData> columnSortHandler;
    private boolean viewConstructed = false;

    public UpPricingStructureHistoryView(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initView();
        initTable(false);

        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        Messages messages = MessagesUtil.getInstance();
        filterDropdown.addItem(messages.getMessage(ColumnsE.USER.getKey()));
        filterDropdown.addItem(messages.getMessage(ColumnsE.DATE_MODIFIED.getKey()));
        filterDropdown.addItem(messages.getMessage(ColumnsE.PRICING_STRUCTURE.getKey()));
    }

    void initTable(boolean refresh) {
        if (dataProvider == null && refresh == false) {
            dataProvider = new IPayDataProvider<UpPricingStructureHistData>(this);

            columnSortHandler = new ListHandler<UpPricingStructureHistData>(dataProvider.getList());
            String headings[] = getHeadings();
            int index = 0;
            for (ColumnsE field : ColumnsE.values()) {
                clltblHistory.addColumn(createColumn(columnSortHandler, field), headings[index]);
                index++;
            }

            clltblHistory.addColumnSortHandler(columnSortHandler);
            dataProvider.addDataDisplay(clltblHistory);
            smplpgrHistory.setDisplay(clltblHistory);
        } else {
            clltblHistory.redraw();
        }
        dateFilter.setDataProvider(dataProvider);
    }

    private void createTable() {
        clltblHistory = new CellTable<UpPricingStructureHistData>(DEFAULT_PAGE_SIZE,
                ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private String[] getHeadings() {
        Messages messagesInstance = MessagesUtil.getInstance();
        String headings[] = new String[ColumnsE.values().length];
        int i = 0;
        for (ColumnsE field : ColumnsE.values()) {
            headings[i++] = messagesInstance.getMessage(field.getKey());
        }
        return headings;
    }

    private Column<UpPricingStructureHistData, ?> createColumn(ListHandler<UpPricingStructureHistData> columnSortHandler,
            final ColumnsE columnType) {
        TextColumn<UpPricingStructureHistData> column = new TextColumn<UpPricingStructureHistData>() {
            @Override
            public String getValue(UpPricingStructureHistData data) {
                Object value = getDataValue(columnType, data);
                if (value instanceof Date) {
                    return FormatUtil.getInstance().formatDateTime((Date) value);
                } else {
                    return getObjectAsText(value);
                }
            }
        };
        columnSortHandler.setComparator(column, new Comparator<UpPricingStructureHistData>() {
            public int compare(UpPricingStructureHistData o1, UpPricingStructureHistData o2) {
                Object value1 = getDataValue(columnType, o1);
                Object value2 = getDataValue(columnType, o2);
                if (value1 == null && value2 == null) {
                    return 0;
                } else if (value1 instanceof Date || value2 instanceof Date) {
                    if (value1 == null) {
                        return 1;
                    } else if (value2 == null) {
                        return -1;
                    }
                    return ((Date) value1).compareTo((Date) value2);
                } else {
                    return value1.toString().compareTo(value2.toString());
                }
            }
        });
        column.setSortable(true);
        return column;
    }

    private Object getDataValue(ColumnsE columnType, UpPricingStructureHistData data) {
        switch (columnType) {
        case START_DATE:
            return data.getStartDate();
        case END_DATE:
            return data.getEndDate();    
        case PRICING_STRUCTURE:
            return data.getPricingStructureName();
        case ACTION:
            return data.getUserAction();
        case DATE_MODIFIED:
            return data.getDateRecModified();
        case USER:
            return data.getUserRecEntered();
        case CHANGE_REASON:
            return data.getPsChangeReasonLogText();
        }
        return null;
    }

    private String getObjectAsText(Object value) {
        if (value == null) {
            return "";
        }
        return String.valueOf(value);
    }

    public void setUpPricingStructureHistDataList(List<UpPricingStructureHistData> thedata) {
        thehistorydata = (ArrayList<UpPricingStructureHistData>) thedata;
        dataProvider.setList(thehistorydata);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<UpPricingStructureHistData>(dataProvider.getList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
        }
        ColumnSortEvent.fire(clltblHistory, clltblHistory.getColumnSortList());
    }

    @Override
    public boolean isValid(UpPricingStructureHistData value, String filter) {
        String filterOption = filterDropdown.getSelectedItemText();
        Messages messages = MessagesUtil.getInstance();
        if (filterOption.equals(messages.getMessage(ColumnsE.DATE_MODIFIED.getKey()))) {
            return dateFilter.isValid(value.getDateRecModified(), filter);
        } else {
            String filterValue = value.getUserRecEntered();
            if (filterOption.equals(messages.getMessage(ColumnsE.PRICING_STRUCTURE.getKey()))) {
                filterValue = value.getPricingStructureName();
            }
            return filterValue.toLowerCase().contains(filter.toLowerCase());
        }
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(thehistorydata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrHistory.firstPage();
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage(ColumnsE.DATE_MODIFIED.getKey()));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }
}
