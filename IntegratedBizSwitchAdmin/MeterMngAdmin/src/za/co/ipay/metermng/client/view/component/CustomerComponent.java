package za.co.ipay.metermng.client.view.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.validation.ClientEnhancedValidator;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.event.AuxAccountEvent;
import za.co.ipay.metermng.client.event.AuxAccountEventHandler;
import za.co.ipay.metermng.client.event.UsagePointUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.customer.ManageNotificationsDialogueBox;
import za.co.ipay.metermng.client.view.component.usercustomfields.ContainsUserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.component.usercustomfields.UserCustomFieldsComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.Customer;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccount;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.userinterface.UserInterfaceFormFields;

public class CustomerComponent extends BaseComponent implements ProvidesResize, RequiresResize, ContainsUserCustomFieldsComponent {

    private static CustomerComponentUiBinder uiBinder = GWT.create(CustomerComponentUiBinder.class);

    @UiField FormRowPanel assignLinkRow;
    @UiField Label lblAssign;
    @UiField Label lblNotification;
    @UiField ListBox titleListBox;
    @UiField TextBox txtbxInitials;
    @UiField TextBox txtbxFirstNames;
    @UiField TextBox txtbxSurname;
    @UiField TextBox txtbxIdNumber;
    @UiField TextBox txtbxCompanyName;
    @UiField TextBox txtbxTax;
    @UiField TextBox txtbxEmail;
    @UiField TextBox txtbxEmail2;
    @UiField TextBox txtbxPhone;
    @UiField TextBox txtbxPhone2;

    @UiField(provided=true) UserCustomFieldsComponent userCustomFieldsComponent;

    @UiField(provided = true) LocationComponent physicalLocationComponent;
    @UiField Button btnSync;

    @UiField DisclosurePanel customerDisclosurePanel;
    @UiField FlowPanel contentPanel;
    @UiField TextBox txtbxAgreementRef;
    @UiField DateBox dtbxStart;
    @UiField Button btnCancel;
    @UiField Button btnSave;
    @UiField Image openorclosearrow;
    @UiField Image customerImage;
    @UiField Label headerLabel;

    @UiField FormElement custTitleElement;
    @UiField FormElement initialsElement;
    @UiField FormElement firstNamesElement;
    @UiField FormElement surnameElement;
    @UiField FormElement idNumberElement;
    @UiField FormElement emailAddressElement;
    @UiField FormElement emailAddressElement2;
    @UiField FormElement companyNameElement;
    @UiField FormElement taxNumberElement;
    @UiField FormElement agreementRefElement;
    @UiField FormElement startDateElement;
    @UiField FormElement phoneNumberElement;
    @UiField FormElement phoneNumberElement2;
    @UiField FormElement freeissueAuxAccountElement;
    @UiField FormElement billableElement;
    @UiField FormElement accountNameElement;
    @UiField FormElement accountBalanceElement;
    @UiField FormElement btnSyncElement;
    @UiField FormElement lowBalanceThresholdElement;
    @UiField FormElement notificationEmailElement;
    @UiField FormElement notificationPhoneElement;

    @UiField IpayListBox lstbxFreeIssue;
    @UiField CheckBox chckbxBillable;
    @UiField TextBox txtbxAccountName;
    @UiField FormElement assignLinkElement;
    @UiField CurrencyTextBox txtbxlowThreshold;
    @UiField TextBox txtbxNotificationEmail;
    @UiField TextBox txtbxNotificationPhone;

    @UiField FlowPanel requiredKeys;
    @UiField HorizontalPanel buttons;
    @UiField Label lblAccountBalance;

    @UiField FormGroupPanel customerAccountPanel;
    @UiField DisclosurePanel physicalAddressPanel;
    @UiField FormRowPanel companyNameTaxNumberPanel;
    @UiField FormRowPanel contactDetailsPanel;
    @UiField FormRowPanel lowBalanceThresholdPanel;
    @UiField FormRowPanel notificationPanel;

    @UiField TextBox txtbxCustRef;
    @UiField FormElement customerRefElement;
    @UiField (provided=true)MridComponent mridComponent;

    private UsagePointData usagePointData = new UsagePointData();

    private AssignCustomerDialogueBox assignCustomerDialogueBox;
    private ManageNotificationsDialogueBox manageNotificationsDialogueBox;
    protected UsagePointWorkspaceView usagePointWorkspaceView;

    private static final Logger logger = Logger.getLogger("CustomerComponent");
    protected Boolean disclosureOpen = null;
    private boolean freeIssueAuxAccountChanged = false;
    private boolean customerChanged = false;
    private boolean customerAgreementChanged = false;
    private boolean customerAccountChanged = false;
    protected HasDirtyData hasDirtyData;

    private boolean autoPopulateAgreementRef = true;
    private boolean autoPopulateAccountName = true;
    private boolean autoPopulateCustomerRef = true;

    private ArrayList<AppSetting> customFieldList;
    private boolean userCustomFieldsComponentVisible = false;
    private Map<String, FormFields> userInterfaceFields;

    private boolean validateAgrRefRegex;

    private boolean validateAccNameRegex;

    interface CustomerComponentUiBinder extends UiBinder<Widget, CustomerComponent> {
    }

    public CustomerComponent(ClientFactory theClientFactory, UsagePointWorkspaceView usagePointWorkspaceView, ArrayList<AppSetting> customFieldList) {
        this.hasDirtyData = usagePointWorkspaceView.createAndRegisterHasDirtyData();
        this.physicalLocationComponent = new LocationComponent(theClientFactory, hasDirtyData);
        this.clientFactory = theClientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.assignCustomerDialogueBox = new AssignCustomerDialogueBox(clientFactory, usagePointWorkspaceView);
        this.manageNotificationsDialogueBox = new ManageNotificationsDialogueBox(clientFactory, usagePointWorkspaceView);
        this.customFieldList = customFieldList;
        userCustomFieldsComponent = new UserCustomFieldsComponent(clientFactory, this, hasDirtyData);
        mridComponent = new MridComponent(clientFactory, hasDirtyData, null, false);
        initWidget(uiBinder.createAndBindUi(this));
        init();
        addFieldHandlers();
    }

    private void init() {
    	if (!clientFactory.isEnableNonBillable()) {
    	    hideBillableCheckBox();
    	}
        configureCustomFields();
        openorclosearrow.setResource(MediaResourceUtil.getInstance().getOpenedArrowImage());
        customerImage.setResource(MediaResourceUtil.getInstance().getUserSmallIcon());
        addHandlers();
        disclosureOpen = null;
        setFetchLink();
        customerDisclosurePanel.setWidth("100%");
        actionPermissions(false);    // remove action buttons if no edit permission
        mapDataToForm();
        dtbxStart.setFormat(new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat())));
        if (physicalLocationComponent != null) {
            physicalLocationComponent.setContainerWorkspace(usagePointWorkspaceView);
        }
        checkEnableSTS(clientFactory.isEnableSTS());
    }

    private void populateTitleListBox() {
        String selectedItem = titleListBox.getSelectedValue();
        if (selectedItem == null || selectedItem.isEmpty()) {
            CustomerAgreementData customerAgreementData = usagePointData.getCustomerAgreementData();
            if(customerAgreementData != null) {
                selectedItem = customerAgreementData.getCustomerData().getTitle();
            }
        }
        titleListBox.clear();
        titleListBox.addItem("", "");
        String configuredValues = userInterfaceFields.get(UserInterfaceFormFields.TITLE).getEnumeratedValues();
        if (configuredValues == null) {
            Messages messages = MessagesUtil.getInstance();
            addTitleItem(messages.getMessage("customer.title.mr"));
            addTitleItem(messages.getMessage("customer.title.mrs"));
            addTitleItem(messages.getMessage("customer.title.ms"));
            addTitleItem(messages.getMessage("customer.title.miss"));
            addTitleItem(messages.getMessage("customer.title.doc"));
            addTitleItem(messages.getMessage("customer.title.prof"));
            addTitleItem(messages.getMessage("customer.title.sir"));
        } else {
            for (String item : configuredValues.split(",")) {
                addTitleItem(item);
            }
        }
        selectTitle(selectedItem);
    }

    private void selectTitle(String title) {
        int index = 0;
        if (title != null) {
            for (int i = 0; i < titleListBox.getItemCount(); i++) {
                if (title.equals(titleListBox.getValue(i))) {
                    index = i;
                    break;
                }
            }
        }
        titleListBox.setSelectedIndex(index);
    }

    private void addTitleItem(String item) {
        titleListBox.addItem(item, item);
    }

    private void actionPermissions(boolean isSetDataCall) {

        // We use setVisible and not removeFromParent since Fetch might be used.
        requiredKeys.setVisible(true);
        buttons.setVisible(true);
        assignLinkRow.setVisible(true);
        btnSyncElement.setVisible(true);

        if (clientFactory.isEnableAccessGroups()) {
            boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointData, clientFactory.isGroupGroupUser());
            if (groupHasGlobal) {
                requiredKeys.setVisible(false);
                buttons.setVisible(false);
                assignLinkRow.setVisible(false);
                btnSyncElement.setVisible(false);
            }

            if (clientFactory.isGroupGlobalUser()) {
                requiredKeys.removeFromParent();
                buttons.removeFromParent();
                assignLinkRow.removeFromParent();
                btnSyncElement.removeFromParent();
            }
        }

        if (isSetDataCall) {
            if (usagePointData.getMeterData() != null
                    && usagePointData.getMeterData().getMeterModelData().isBalanceSync()
                    && usagePointData.getCustomerAgreementData() != null) {
                if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_CUST_ACC_SYNC_BALANCE)) {
                    btnSyncElement.removeFromParent();
                }
                //fine, leave button in panel
            } else {
                btnSyncElement.removeFromParent();
            }
        }

        if (!clientFactory.getUser().hasPermission(MeterMngStatics.EDIT_PERMISSION_MM_CUSTOMER_EDIT)) {
            requiredKeys.removeFromParent();
            buttons.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_CUST_ASSIGN)) {
            assignLinkRow.removeFromParent();
        }
        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_CUST_ACC_SYNC_BALANCE)) {
            btnSyncElement.removeFromParent();
        }
    }

    public void hideCustomerAccountPanel(boolean isHidePanel) {
        customerAccountPanel.setVisible(!isHidePanel);
    }

    public void checkEnableSTS(boolean enableSTS) {
        if (!enableSTS) {
            freeissueAuxAccountElement.removeFromParent();
            lstbxFreeIssue.removeFromParent();
        }
    }

    public void setAutoPopulateAgreementRef() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.AUTO_POPULATE_AGREEMENT_REF, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                if (result != null) {
                    autoPopulateAgreementRef = Boolean.parseBoolean(result.getValue());
                } else {
                    autoPopulateAgreementRef = Boolean.TRUE;
                }
            }
        });
    }
    public void setAutoPopulateAccountName() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.AUTO_POPULATE_ACCOUNT_NAME, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                if (result != null) {
                    autoPopulateAccountName = Boolean.parseBoolean(result.getValue());
                } else {
                    autoPopulateAccountName = Boolean.TRUE;
                }
            }
        });
    }
    public void setAutoPopulateCustomerRef() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.AUTO_POPULATE_CUSTOMER_REF, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                if (result != null) {
                    autoPopulateCustomerRef = Boolean.parseBoolean(result.getValue());
                } else {
                    autoPopulateCustomerRef = Boolean.TRUE;
                }
            }
        });
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        usagePointWorkspaceView.toggleSaveBtns(false);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePointData != null) {
                    usagePointData.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointData, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleSaveButton();
                            } else {
                                Place place = usagePointWorkspaceView.getPlace();
                                usagePointWorkspaceView.processInvalidState(place);
                            }
                        }
                    });
                } else {
                    continueHandleSaveButton();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void continueHandleSaveButton() {
        clearErrors();
        if (validate()) {
            if (clientFactory.isEnableNonBillable() && billableElement.isVisible() && !chckbxBillable.getValue()) {
                Messages msgInstance = MessagesUtil.getInstance();
                Dialogs.confirm(msgInstance.getMessage("customer.agreement.billable.setting.check", new String[] {chckbxBillable.getValue().toString()}),
                        msgInstance.getMessage("option.positive"),
                        msgInstance.getMessage("option.negative"),
                        MediaResourceUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            completeSaveCustomer();
                        } else {
                            usagePointWorkspaceView.toggleSaveBtns(true);
                        }
                    }
                },
                        btnSave.getAbsoluteLeft() + 100,
                        btnSave.getAbsoluteTop());
            } else {
                completeSaveCustomer();
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("customer.save.errors"), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(), btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(), null);
            usagePointWorkspaceView.toggleSaveBtns(true);
        }
    }
    
    private void completeSaveCustomer() {
        // Map form fields to data object
        ClientCallback<CustomerAgreementData> customerSvcAsyncCallback = new ClientCallback<CustomerAgreementData>() {
            @Override
            public void onSuccess(CustomerAgreementData result) {
                hasDirtyData.setDirtyData(false);
                int left = btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth();
                int top = btnSave.getAbsoluteTop() - btnSave.getOffsetHeight();
                UsagePointUpdatedEvent updateevent = null;
                usagePointData.setCustomerAgreementData(result);

                if (usagePointData.getId() != null) {
                    // If there is an associated usage point and the customer being saved is not the customer currently
                    // associated with it, assign new customer
                    if (!result.getCustomerId().equals(usagePointData.getCustomerAgreementData().getCustomerId())) {
                        logger.info("Save button do assign");
                        updateevent = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePointData, UsagePointUpdatedEvent.ASSIGN_CUSTOMER, top, left);
                        updateevent.setAssignToCustomerId(usagePointData.getCustomerAgreementData().getCustomerId());
                    }
                }
                if (updateevent == null) {
                    updateevent = new UsagePointUpdatedEvent(usagePointWorkspaceView, usagePointData, UsagePointUpdatedEvent.SAVE_CUSTOMER, top, left);
                }
                clientFactory.getEventBus().fireEvent(updateevent);

                if (freeIssueAuxAccountChanged) {
                    clientFactory.getEventBus().fireEvent(new AuxAccountEvent(result.getId(), result.getFreeIssueAuxAccId(), AuxAccountEvent.AUX_ACNT_FREE_ISSUE_SELECTED));
                }
                mapDataToForm();
            }
            
            @Override
            public void onFailure(Throwable caught) {
            	usagePointWorkspaceView.toggleSaveBtns(true);
            	usagePointData = new UsagePointData();
            	super.onFailure(caught);
            }
        };

        freeIssueAuxAccountChanged = false;
        customerChanged = false;
        customerAgreementChanged = false;
        customerAccountChanged = false;
        mapFormToData();
        if (clientFactory != null) {
            clientFactory.getCustomerRpc().updateCustomer(usagePointData.getCustomerAgreementData(), customerChanged, customerAgreementChanged, customerAccountChanged, customerSvcAsyncCallback);
        }
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if(confirm) {
                    hasDirtyData.setDirtyData(false);
                    clearErrors();
                    // clear custom fields
                    userCustomFieldsComponent.clearCustomFields();

                    if (clientFactory != null) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution sessionCheckResolution) {
                                mapDataToForm();
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("customer.changes.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft() + btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop() - btnCancel.getOffsetHeight(), null);

                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            }
        });
    }

    @UiHandler("btnSync")
    void handleSyncAccBalButton(ClickEvent event) {
        final CustomerAgreementData customerAgreementData = usagePointData.getCustomerAgreementData();
        final int left = btnSync.getAbsoluteLeft() + btnSync.getOffsetWidth();
        final int top = btnSync.getAbsoluteTop() - btnSync.getOffsetHeight();
        if (customerAgreementData == null) {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("customer.account.does.not.exist"),
                    MediaResourceUtil.getInstance().getInformationIcon(), left, top, null);
        } else {
            btnSync.setEnabled(false);
            clearErrors();
            // Map form fields to data object
            if (clientFactory != null) {
                final ClientCallback<IpayResponseData> syncAccBalSvcAsyncCallback = new ClientCallback<IpayResponseData>() {
                    @Override
                    public void onSuccess(IpayResponseData result) {
                        if (result == null) {
                            Dialogs.displayErrorMessage(
                                    MessagesUtil.getInstance().getMessage("customer.sync.accbal.connection.error"),
                                    MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                    MessagesUtil.getInstance().getMessage("button.close"));
                        } else if (!result.getResCode().equals("mdc000") && !result.getResCode().equals("mdc010")
                                && !result.getResCode().equals("mdc011")) {
                            Dialogs.displayErrorMessage(
                                    MessagesUtil.getInstance().getMessage("customer.sync.accbal.error",
                                            new String[] { result.getResRef() }),
                                    MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                    MessagesUtil.getInstance().getMessage("button.close"));
                        } else {
                            String mesg = "customer.sync.accbal.ok.mdc000";
                            if (result.getResCode().equals("mdc010")) {
                                mesg = "customer.sync.accbal.ok.mdc010";
                            } else if (result.getResCode().equals("mdc011")) {
                                mesg = "customer.sync.accbal.ok.mdc011";
                            }
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance().getMessage(mesg, new String[] { result.getResRef() }),
                                    MediaResourceUtil.getInstance().getInformationIcon(), left, top, null);
                        }
                        btnSync.setEnabled(true);
                    }
                };
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution sessionCheckResolution) {
                        clientFactory.getCustomerRpc().sendSyncAccountBalanceMsg(usagePointData.getMeterData(),
                            customerAgreementData.getCustomerAccount().getId(), syncAccBalSvcAsyncCallback);
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        }
    }

    @UiHandler("lblAssign")
    void handleAssignLink(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if(usagePointData != null) {
                    usagePointData.setMultiUsagePointEnabled(clientFactory.isEnableMultiUp());
                    clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointData, new ClientCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean result) {
                            if(result) {
                                continueHandleAssignLink();
                            } else {
                                Place place = usagePointWorkspaceView.getPlace();
                                usagePointWorkspaceView.processInvalidState(place);
                            }
                        }
                    });
                } else {
                    continueHandleAssignLink();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("lblNotification")
    void handleNotificationLink(ClickEvent event) {
        clearErrors();
        if(usagePointData != null && usagePointData.getCustomerAgreementData() != null) {
            manageNotificationsDialogueBox.setUsagePointData(usagePointData);
            manageNotificationsDialogueBox.setNotificationData();
            manageNotificationsDialogueBox.setGlassEnabled(true);
            manageNotificationsDialogueBox.setAutoHideEnabled(false);
            manageNotificationsDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
            manageNotificationsDialogueBox.setAnimationEnabled(true);
            manageNotificationsDialogueBox.setText(MessagesUtil.getInstance().getMessage("customer.notification.types") + ":");
            int left = lblNotification.getAbsoluteLeft() + lblNotification.getOffsetWidth();
            int top = lblNotification.getAbsoluteTop() + lblNotification.getOffsetHeight();
            manageNotificationsDialogueBox.setPopupPosition(left, top);
            manageNotificationsDialogueBox.show();
            MeterMngClientUtils.ensurePopupIsOnScreen(manageNotificationsDialogueBox, left, top, 0);
        }else {
            Dialogs.displayInformationMessage(
                    MessagesUtil.getInstance().getMessage("customer.account.does.not.exist"),
                    MediaResourceUtil.getInstance().getInformationIcon(),
                    lblNotification.getAbsoluteLeft(), lblNotification.getAbsoluteTop(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    private void continueHandleAssignLink() {
        Messages messages = MessagesUtil.getInstance();
        String lblAssignText = lblAssign.getText();
        if (lblAssignText.equals(messages.getMessage("customer.unassign"))) {
            clearErrors();
            if (clientFactory != null) {
                UnassignCustomerDialogueBox unassignCustomerDialogueBox = new UnassignCustomerDialogueBox(clientFactory,
                        usagePointWorkspaceView);
                unassignCustomerDialogueBox.setUsagePointData(usagePointData);
                unassignCustomerDialogueBox.setGlassEnabled(true);
                unassignCustomerDialogueBox.setAutoHideEnabled(false);
                unassignCustomerDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
                unassignCustomerDialogueBox.setAnimationEnabled(true);
                unassignCustomerDialogueBox.setText(messages.getMessage("customer.unassign") + ":");
                int left = lblAssign.getAbsoluteLeft() + lblAssign.getOffsetWidth() - 250;
                int top = lblAssign.getAbsoluteTop() + lblAssign.getOffsetHeight();
                unassignCustomerDialogueBox.setPopupPosition(left, top);
                unassignCustomerDialogueBox.show();
                MeterMngClientUtils.ensurePopupIsOnScreen(unassignCustomerDialogueBox, left, top, 0);
            }
        } else if (lblAssignText.equals(messages.getMessage("customer.assign"))
                || lblAssignText.equals(messages.getMessage("customer.fetch"))) {
            clearErrors();
            assignCustomerDialogueBox.clearFields();
            assignCustomerDialogueBox.setUsagePointData(usagePointData);
            assignCustomerDialogueBox.setGlassEnabled(true);
            assignCustomerDialogueBox.setAutoHideEnabled(false);
            assignCustomerDialogueBox.setAutoHideOnHistoryEventsEnabled(true);
            assignCustomerDialogueBox.setAnimationEnabled(true);
            assignCustomerDialogueBox.setText(messages.getMessage("customer.title.find") + ":");
            int left = lblAssign.getAbsoluteLeft() + lblAssign.getOffsetWidth() - 250;
            int top = lblAssign.getAbsoluteTop() + lblAssign.getOffsetHeight();
            assignCustomerDialogueBox.setPopupPosition(left, top);
            assignCustomerDialogueBox.show();
            MeterMngClientUtils.ensurePopupIsOnScreen(assignCustomerDialogueBox, left, top, 0);
        }
    }

    private void addHandlers() {
        this.customerDisclosurePanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {

            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getOpenedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.TRUE;
            }
        });

        this.customerDisclosurePanel.addCloseHandler(new CloseHandler<DisclosurePanel>() {

            @Override
            public void onClose(CloseEvent<DisclosurePanel> event) {
                openorclosearrow.setUrl(MediaResourceUtil.getInstance().getClosedArrowImage().getSafeUri().asString());
                disclosureOpen = Boolean.FALSE;
            }
        });

        clientFactory.getEventBus().addHandler(AuxAccountEvent.TYPE, new AuxAccountEventHandler() {

            @Override
            public void processAuxAccountEvent(AuxAccountEvent event) {
                if (usagePointData.getCustomerAgreementId() != null) {
                    if (event.getCustomerAgreementId() != null && event.getCustomerAgreementId().equals(usagePointData.getCustomerAgreementId())) {
                        if (event.getAuxAccountEventType() == AuxAccountEvent.AUX_ACNT_FREE_ISSUE_SELECTED) {
                            usagePointData.getCustomerAgreementData().setFreeIssueAuxAccId(event.getAuxAccountId());
                        } else {
                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                @Override
                                public void callback(SessionCheckResolution sessionCheckResolution) {
                                    populateAuxAccountLookupListBox();
                                }
                            };
                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                        }
                    }
                }
            }
        });

    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
        if(usagePointData != null && usagePointData.getCustomerAgreementData() != null && manageNotificationsDialogueBox != null) {
            manageNotificationsDialogueBox.setUsagePointData(usagePointData);
            manageNotificationsDialogueBox.setNotificationData();
        }
        assignLinkElement.setVisible(true);
        assignCustomerDialogueBox.setBtnAssignCustomerText("customer.assign.short");
        if (usagePointData.getCustomerAgreementData() != null && usagePointData.getId() != null) {
            lblAssign.setText(MessagesUtil.getInstance().getMessage("customer.unassign"));
            assignLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("customer.unassign.help"));
        } else if (usagePointData.getId() != null) {
            lblAssign.setText(MessagesUtil.getInstance().getMessage("customer.assign"));
            assignLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("customer.assign.help"));
            assignCustomerDialogueBox.setIsFetch(false);
        } else {
            setFetchLink();
        }
        if (lstbxFreeIssue.getListBox().getItemCount() == 0) {
            populateAuxAccountLookupListBox();
        }

        actionPermissions(true);
        mapDataToForm();
    }

    private void setFetchLink() {
        assignLinkElement.setVisible(true);
        lblAssign.setText(MessagesUtil.getInstance().getMessage("customer.fetch"));
        assignLinkElement.setHelpMsg(MessagesUtil.getInstance().getMessage("customer.fetch.help"));
        assignCustomerDialogueBox.setIsFetch(true);
        assignCustomerDialogueBox.setBtnAssignCustomerText("customer.fetch");
    }

    public ClientFactory getClientFactory() {
        return clientFactory;
    }

    public void setClientFactory(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
    }

    public void mapDataToForm() {
        // get auto populate settings. Have to do this every time because otherwise run into async issues!
        ArrayList<String> autoPopKeys = new ArrayList<String>();
        autoPopKeys.add(AppSettings.AUTO_POPULATE_ACCOUNT_NAME);
        autoPopKeys.add(AppSettings.AUTO_POPULATE_AGREEMENT_REF);
        autoPopKeys.add(AppSettings.AUTO_POPULATE_CUSTOMER_REF);
        clientFactory.getAppSettingRpc().getAppSettingsByKeys(autoPopKeys, new ClientCallback<Map<String, AppSetting>>() {
            @Override
            public void onSuccess(Map<String, AppSetting> result) {
                if (result != null) {
                    autoPopulateAccountName = Boolean.parseBoolean(result.get(AppSettings.AUTO_POPULATE_ACCOUNT_NAME).getValue());
                    autoPopulateAgreementRef = Boolean.parseBoolean(result.get(AppSettings.AUTO_POPULATE_AGREEMENT_REF).getValue());
                    autoPopulateCustomerRef = Boolean.parseBoolean(result.get(AppSettings.AUTO_POPULATE_CUSTOMER_REF).getValue());
                } else {
                    autoPopulateAccountName = Boolean.TRUE;
                    autoPopulateAgreementRef = Boolean.TRUE;
                    autoPopulateCustomerRef = Boolean.TRUE;
                }
                completeMapDataToForm();
            }
        });
    }
    private void completeMapDataToForm() {
        assignCustomerDialogueBox.hide();

        if (usagePointData.getCustomerAgreementData() != null && usagePointData.getCustomerAgreementData().getCustomerData() != null) {
            CustomerData customerData = usagePointData.getCustomerAgreementData().getCustomerData();
            titleListBox.setSelectedIndex(getIndexOfTitle(customerData.getTitle()));
            txtbxInitials.setText(customerData.getInitials());
            txtbxFirstNames.setText(customerData.getFirstnames());
            txtbxSurname.setText(customerData.getSurname());
            txtbxIdNumber.setText(customerData.getIdNumber());
            txtbxCompanyName.setText(customerData.getCompanyName());
            txtbxTax.setText(customerData.getTaxNum());
            txtbxEmail.setText(customerData.getEmail1());
            txtbxEmail2.setText(customerData.getEmail2());
            txtbxPhone.setText(customerData.getPhone1());
            txtbxPhone2.setText(customerData.getPhone2());
            mridComponent.setMrid(customerData.getMrid());
            mridComponent.setIsExternal(customerData.isMridExternal());
            txtbxCustRef.setText(customerData.getCustomerReference());
            chckbxBillable.setValue(usagePointData.getCustomerAgreementData().isBillable());
            if (usagePointData.getCustomerAgreementData().getAgreementRef() == null || usagePointData.getCustomerAgreementData().getAgreementRef().trim().isEmpty()) {
                generateAgreementRef();
            } else {
                txtbxAgreementRef.setText(usagePointData.getCustomerAgreementData().getAgreementRef());
            }
            if (usagePointData.getCustomerAgreementData().getCustomerAccount() != null) {
                if (usagePointData.getCustomerAgreementData().getCustomerAccount().getAccountName() == null
                        || usagePointData.getCustomerAgreementData().getCustomerAccount().getAccountName().trim().isEmpty()) {
                    generateAccountName();
                } else {
                    txtbxAccountName.setText(usagePointData.getCustomerAgreementData().getCustomerAccount().getAccountName());
                }
                txtbxlowThreshold.setAmount(usagePointData.getCustomerAgreementData().getCustomerAccount().getLowBalanceThreshold());
                if (usagePointData.getCustomerAgreementData().getCustomerAccount().getAccountBalance() == null) {
                    usagePointData.getCustomerAgreementData().getCustomerAccount().setAccountBalance(BigDecimal.ZERO);
                }
                lblAccountBalance.setText(FormatUtil.getInstance().formatCurrency(usagePointData.getCustomerAgreementData().getCustomerAccount().getAccountBalance(), true));
                txtbxNotificationEmail.setText(usagePointData.getCustomerAgreementData().getCustomerAccount().getNotificationEmail());
                txtbxNotificationPhone.setText(usagePointData.getCustomerAgreementData().getCustomerAccount().getNotificationPhone());
            }

            if (usagePointData.getCustomerAgreementData().getStartDate() != null) {
                dtbxStart.setValue(usagePointData.getCustomerAgreementData().getStartDate());
            }

            if (usagePointData.getCustomerAgreementData().getFreeIssueAuxAccId() != null) {
                lstbxFreeIssue.selectItemByValue(usagePointData.getCustomerAgreementData().getFreeIssueAuxAccId().toString());
            }

            headerLabel.setText(MessagesUtil.getInstance().getMessage("customer.title") + ": " + customerData.getName());

            if (userCustomFieldsComponentVisible) {
                mapUserCustomFields(customerData);
            }
            physicalLocationComponent.setLocation(customerData.getPhysicalLocation());
        } else {
            clearCustomerComponent(usagePointData);
        }

        if (clientFactory.isEnableNonBillable() && usagePointData != null && usagePointData.getMeterData() != null
        		&& usagePointData.getMeterData().getMeterModelData().getMeterTypeId().equals(MeterTypeE.STS.getId())) {
        	billableElement.setVisible(false);
        }
    }

    private int getIndexOfTitle(String title) {

        int index = 0; // If title is null
        if (title != null) {
            for (int i = 0; i < titleListBox.getItemCount(); i++) {
                if (titleListBox.getValue(i).equals(title)) {
                    index = i;
                    break;
                }
            }
        }
        return index;
    }

    private void generateAgreementRef() {
        if (txtbxAgreementRef.getValue().isEmpty() && autoPopulateAgreementRef 
                && userInterfaceFields != null && !validateAgrRefRegex) {
            clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    txtbxAgreementRef.setText("AGR" + result);
                }
            });
        }   //else, leave it empty
    }
    
    private void generateAccountName() {
        if (txtbxAccountName.getValue().isEmpty() && autoPopulateAccountName 
                && userInterfaceFields != null && !validateAccNameRegex) {
            clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    txtbxAccountName.setText("ACC" + result);
                }
            });
        }  //else, leave it empty
    }
    private void generateCustomerRef() {
        if (txtbxCustRef.getValue().isEmpty() && autoPopulateCustomerRef) {
            clientFactory.getUsagePointRpc().getAutoGeneratedRef(new ClientCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    txtbxCustRef.setText("CUST" + result);
                }
            });
        }  //else, leave it empty
    }

    private void mapUserCustomFields(CustomerData customerData) {
        if (customerData == null) {
            return;
        }
        userCustomFieldsComponent.mapDataToForm(customerData.getCustomVarchar1(),
                customerData.getCustomVarchar2(),
                customerData.getCustomNumeric1(),
                customerData.getCustomNumeric2(),
                customerData.getCustomTimestamp1(),
                customerData.getCustomTimestamp2());
    }

    public void clearCustomerComponent(UsagePointData upData) {
        // when add /unassign customer want a fresh component
        txtbxAgreementRef.setValue("");
        lstbxFreeIssue.setSelectedIndex(0);
        txtbxAccountName.setValue("");
        lblAccountBalance.setText(FormatUtil.getInstance().formatCurrency(0, true));
        txtbxCustRef.setValue("");

        titleListBox.setSelectedIndex(0);
        txtbxInitials.setText("");
        txtbxFirstNames.setText("");
        txtbxSurname.setText("");
        txtbxIdNumber.setText("");
        txtbxCompanyName.setText("");
        txtbxTax.setText("");
        txtbxEmail.setText("");
        txtbxEmail2.setText("");
        txtbxPhone.setText("");
        txtbxPhone2.setText("");
        dtbxStart.setValue(null);
        physicalLocationComponent.setLocation(null);
        txtbxlowThreshold.setValue(null);       //note setAmount(null) sets value to 0.00! setValue(null) to empty!
        txtbxNotificationEmail.setText("");
        txtbxNotificationPhone.setText("");
        chckbxBillable.setValue(true);
        mridComponent.setMrid(null);
        mridComponent.initMrid(clientFactory);
        headerLabel.setText(MessagesUtil.getInstance().getMessage("customer.add"));

        if(upData != null && upData.getCustomerAgreementData() != null && upData.getCustomerAgreementData().getAgreementRef() != null) {
            // keep the already generated ref if there is one
            txtbxAgreementRef.setText(upData.getCustomerAgreementData().getAgreementRef());
        } else {
            generateAgreementRef();
        }
        if (upData != null && upData.getCustomerAgreementData() != null
                && upData.getCustomerAgreementData().getCustomerAccount() != null
                && upData.getCustomerAgreementData().getCustomerAccount().getAccountName() != null) {
            // keep the already generated ref if there is one
            txtbxAccountName.setText(upData.getCustomerAgreementData().getCustomerAccount().getAccountName());
        } else {
            generateAccountName();
        }
        if(upData != null && upData.getCustomerAgreementData() != null
                && upData.getCustomerAgreementData().getCustomerData().getCustomerReference() != null
                && !upData.getCustomerAgreementData().getCustomerData().getCustomerReference().isEmpty()) {
            txtbxCustRef.setText(upData.getCustomerAgreementData().getCustomerData().getCustomerReference());
        } else {
            generateCustomerRef();
        }

        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearCustomFields();
        }
    }

    public void refreshAccountBalance(final BigDecimal customerBalance) {
        usagePointData.getCustomerAgreementData().getCustomerAccount().setAccountBalance(customerBalance);
        Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
            @Override
            public void execute() {
                lblAccountBalance.setText(FormatUtil.getInstance().formatCurrency(customerBalance, true));
            }
        });
    }

    public void mapFormToData() {
        CustomerAgreementData customerAgreementData;
        CustomerData customerData = null;
        CustomerAccount customerAccount = null;
        String temp;

        if (usagePointData.getCustomerAgreementData() == null) {
            customerAgreementData = new CustomerAgreementData();
            customerAgreementChanged = true;
        } else {
            customerAgreementData = usagePointData.getCustomerAgreementData();
            if (customerAgreementData.getCustomerData() != null) {
                customerData = customerAgreementData.getCustomerData();
            }
            if (customerAgreementData.getCustomerAccount() != null) {
                customerAccount = customerAgreementData.getCustomerAccount();
            }
        }
        if (customerData == null) {
            customerData = new CustomerData();
            customerChanged = true;
        }
        if (customerAccount == null) {
            customerAccount = new CustomerAccount();
            customerAccountChanged = true;
        }
        temp = customerData.getTitle();
        if (titleListBox.getValue(titleListBox.getSelectedIndex()).isEmpty()) {
        	customerData.setTitle(null);
        } else {
        	customerData.setTitle(titleListBox.getValue(titleListBox.getSelectedIndex()));
        }
        if ((temp == null && customerData.getTitle() != null) || (temp != null && customerData.getTitle() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getTitle() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getTitle()));
        }

        temp = customerData.getInitials();
        if (txtbxInitials.getText().isEmpty()) {
            customerData.setInitials(null);
        } else {
            customerData.setInitials(txtbxInitials.getText());
        }
        if ((temp == null && customerData.getInitials() != null) || (temp != null && customerData.getInitials() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getTitle() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getInitials()));
        }

        temp = customerData.getFirstnames();
        if (txtbxFirstNames.getText().isEmpty()) {
            customerData.setFirstnames(null);
        } else {
            customerData.setFirstnames(txtbxFirstNames.getText());
        }
        if ((temp == null && customerData.getFirstnames() != null) || (temp != null && customerData.getFirstnames() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getFirstnames() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getFirstnames()));
        }

        temp = customerData.getSurname();
        if (txtbxSurname.getText().isEmpty()) {
            customerData.setSurname(null);
        } else {
            customerData.setSurname(txtbxSurname.getText());
        }
        if ((temp == null && customerData.getSurname() != null) || (temp != null && customerData.getSurname() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getSurname() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getSurname()));
        }

        temp = customerData.getIdNumber();
        if (txtbxIdNumber.getText().isEmpty()) {
            customerData.setIdNumber(null);
        } else {
            customerData.setIdNumber(txtbxIdNumber.getText());
        }
        if ((temp == null && customerData.getIdNumber() != null) || (temp != null && customerData.getIdNumber() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getIdNumber() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getIdNumber()));
        }

        temp = customerData.getCompanyName();
        if (txtbxCompanyName.getText().isEmpty()) {
            customerData.setCompanyName(null);
        } else {
            customerData.setCompanyName(txtbxCompanyName.getText());
        }
        if ((temp == null && customerData.getCompanyName() != null) || (temp != null && customerData.getCompanyName() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getCompanyName() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getCompanyName()));
        }

        temp = customerData.getTaxNum();
        if (txtbxTax.getText().isEmpty()) {
            customerData.setTaxNum(null);
        } else {
            customerData.setTaxNum(txtbxTax.getText());
        }
        if ((temp == null && customerData.getTaxNum() != null) || (temp != null && customerData.getTaxNum() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getTaxNum() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getTaxNum()));
        }

        temp = customerData.getEmail1();
        if (txtbxEmail.getText().isEmpty()) {
            customerData.setEmail1(null);
        } else {
            customerData.setEmail1(txtbxEmail.getText());
        }
        if ((temp == null && customerData.getEmail1() != null) || (temp != null && customerData.getEmail1() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getEmail1() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getEmail1()));
        }

        temp = customerData.getEmail2();
        if (txtbxEmail2.getText().isEmpty()) {
            customerData.setEmail2(null);
        } else {
            customerData.setEmail2(txtbxEmail2.getText());
        }
        if ((temp == null && customerData.getEmail2() != null) || (temp != null && customerData.getEmail2() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getEmail2() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getEmail2()));
        }

        temp = customerData.getPhone1();
        if (txtbxPhone.getText().isEmpty()) {
            customerData.setPhone1(null);
        } else {
            customerData.setPhone1(txtbxPhone.getText());
        }
        if ((temp == null && customerData.getPhone1() != null) || (temp != null && customerData.getPhone1() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getPhone1() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getPhone1()));
        }

        temp = customerData.getPhone2();
        if (txtbxPhone2.getText().isEmpty()) {
            customerData.setPhone2(null);
        } else {
            customerData.setPhone2(txtbxPhone2.getText());
        }

        if ((temp == null && customerData.getPhone2() != null) || (temp != null && customerData.getPhone2() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getPhone2() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getPhone2()));
        }
        ///
        temp = customerData.getMrid();
        if (mridComponent.getMrid().isEmpty()) {
            customerData.setMrid(null);
        } else {
            customerData.setMrid(mridComponent.getMrid());
            customerData.setMridExternal(mridComponent.isExternal());
        }
        if ((temp == null && customerData.getMrid() != null) || (temp != null && customerData.getMrid() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getMrid() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getMrid()));
        }

        temp = customerData.getCustomerReference();
        if (txtbxCustRef.getText().isEmpty()) {
            customerData.setCustomerReference(null);
        } else {
            customerData.setCustomerReference(txtbxCustRef.getText());
        }
        if ((temp == null && customerData.getCustomerReference() != null) || (temp != null && customerData.getCustomerReference() == null)) {
            customerChanged = true;
        } else if (temp != null && customerData.getCustomerReference() != null) {
            if (!customerChanged)
                customerChanged = (!temp.equals(customerData.getCustomerReference()));
        }

        // Location
        physicalLocationComponent.mapFormToData();
        customerData.setPhysicalLocation(physicalLocationComponent.getLocation());

        temp = customerAgreementData.getAgreementRef();
        if (txtbxAgreementRef.getText().isEmpty()) {
            customerAgreementData.setAgreementRef(null);
        } else {
            customerAgreementData.setAgreementRef(txtbxAgreementRef.getText());
        }
        if ((temp == null && customerAgreementData.getAgreementRef() != null) || (temp != null && customerAgreementData.getAgreementRef() == null)) {
            customerAgreementChanged = true;
        } else if (temp != null && customerAgreementData.getAgreementRef() != null) {
            if (!customerAgreementChanged)
                customerAgreementChanged = (!temp.equals(customerAgreementData.getAgreementRef()));
        }

        if (customerAgreementData.getStartDate() != null) {
            temp = customerAgreementData.getStartDate().toString();
        } else {
            temp = null;
        }
        customerAgreementData.setStartDate(dtbxStart.getValue());
        if ((temp == null && customerAgreementData.getStartDate() != null) || (temp != null && customerAgreementData.getStartDate() == null)) {
            customerAgreementChanged = true;
        } else if (temp != null && customerAgreementData.getStartDate() != null) {
            if (!customerAgreementChanged)
                customerAgreementChanged = (!temp.equals(customerAgreementData.getStartDate().toString()));
        }

        if (chckbxBillable.isAttached() && chckbxBillable.isEnabled()) {
        	if (customerAgreementData.getId() != null) {
        		if ((chckbxBillable.getValue() && !customerAgreementData.isBillable())
        				|| (!chckbxBillable.getValue() && customerAgreementData.isBillable())) {
        			customerAgreementData.setBillable(chckbxBillable.getValue());
        			customerAgreementChanged = true;
        		}
        	} else {
        		customerAgreementData.setBillable(chckbxBillable.getValue());
    			customerAgreementChanged = true;
        	}
        }

        if (txtbxAccountName.getText() == null || txtbxAccountName.getText().isEmpty()) {
            if (customerAccount.getAccountName() != null && !(customerAccount.getAccountName().isEmpty())) {
                customerAccountChanged = true;
            }
            customerAccount.setAccountName(null);
        } else if (customerAccount.getAccountName() == null || !(txtbxAccountName.getText().equals(customerAccount.getAccountName()))) {
            customerAccount.setAccountName(txtbxAccountName.getText());
            customerAccountChanged = true;
        }

        if (txtbxlowThreshold.getAmount() == null) {
            if (customerAccount.getLowBalanceThreshold() != null) {            // && !(customerAccount.getLowBalanceThreshold().equals(BigDecimal.ZERO))) {
                customerAccountChanged = true;
            }
            customerAccount.setLowBalanceThreshold(null);
        } else if (customerAccount.getLowBalanceThreshold() == null || !(txtbxlowThreshold.getAmount().equals(customerAccount.getLowBalanceThreshold()))) {
            customerAccount.setLowBalanceThreshold(txtbxlowThreshold.getAmount());
            customerAccountChanged = true;
        }

        if (txtbxNotificationEmail.getText() == null || txtbxNotificationEmail.getText().isEmpty()) {
            if (customerAccount.getNotificationEmail() != null && !(customerAccount.getNotificationEmail().isEmpty())) {
                customerAccountChanged = true;
            }
            customerAccount.setNotificationEmail(null);
        } else if (customerAccount.getNotificationEmail() == null || !(txtbxNotificationEmail.getText().equals(customerAccount.getNotificationEmail()))) {
            customerAccount.setNotificationEmail(txtbxNotificationEmail.getText());
            customerAccountChanged = true;
        }

        if (txtbxNotificationPhone.getText() == null || txtbxNotificationPhone.getText().isEmpty()) {
            if (customerAccount.getNotificationPhone() != null && !(customerAccount.getNotificationPhone().isEmpty())) {
                customerAccountChanged = true;
            }
            customerAccount.setNotificationPhone(null);
        } else if (customerAccount.getNotificationPhone() == null || !(txtbxNotificationPhone.getText().equals(customerAccount.getNotificationPhone()))) {
            customerAccount.setNotificationPhone(txtbxNotificationPhone.getText());
            customerAccountChanged = true;
        }
        customerData.setRecordStatus(RecordStatus.ACT);
        customerAgreementData.setRecordStatus(RecordStatus.ACT);

        if (lstbxFreeIssue.getSelectedValues().size() == 0 || lstbxFreeIssue.getSelectedValues().get(0).trim().isEmpty()) {
            freeIssueAuxAccountChanged = customerAgreementData.getFreeIssueAuxAccId() != null;
            customerAgreementData.setFreeIssueAuxAccId(null);
        } else {
            Long selectedId = Long.valueOf(lstbxFreeIssue.getSelectedValues().get(0));
            freeIssueAuxAccountChanged = (customerAgreementData.getFreeIssueAuxAccId() == null || !customerAgreementData.getFreeIssueAuxAccId().equals(selectedId));
            if (!customerAgreementChanged)
                customerAgreementChanged = freeIssueAuxAccountChanged;
            customerAgreementData.setFreeIssueAuxAccId(selectedId);
            lstbxFreeIssue.selectItemByValue(customerAgreementData.getFreeIssueAuxAccId().toString());
        }

        if (userCustomFieldsComponent.mapFormToData(customerData)) {
        	customerChanged = true;
        }

        customerAgreementData.setCustomerData(customerData);
        customerAgreementData.setCustomerAccount(customerAccount);
        usagePointData.setCustomerAgreementData(customerAgreementData);
    }

    private boolean validate() {
        boolean validated = true;

        Customer customer = new Customer();
        customer.setSurname(txtbxSurname.getText());
        customer.setIdNumber(txtbxIdNumber.getText());
        customer.setFirstnames(txtbxFirstNames.getText());
        customer.setInitials(txtbxInitials.getText());
        if (titleListBox.getValue(titleListBox.getSelectedIndex()).isEmpty()) {
            customer.setTitle(null);
        } else {
            customer.setTitle(titleListBox.getValue(titleListBox.getSelectedIndex()));
        }
        customer.setCompanyName(txtbxCompanyName.getText());
        customer.setTaxNum(txtbxTax.getText());
        customer.setEmail1(txtbxEmail.getText());
        customer.setEmail2(txtbxEmail2.getText());
        customer.setPhone1(txtbxPhone.getText());
        customer.setPhone2(txtbxPhone2.getText());

        customer.setCustomerReference(txtbxCustRef.getText());
        customer.setMrid(mridComponent.getMrid());
        customer.setMridExternal(mridComponent.isExternal());

        CustomerAgreementData customerAgreementData = new CustomerAgreementData();
        customerAgreementData.setAgreementRef(txtbxAgreementRef.getText());
        customerAgreementData.setStartDate(dtbxStart.getValue());
        customerAgreementData.setBillable(chckbxBillable.getValue());

        CustomerAccount customerAccount = new CustomerAccount();
        customerAccount.setAccountName(txtbxAccountName.getText());
        customerAccount.setLowBalanceThreshold(txtbxlowThreshold.getAmount());
        customerAccount.setNotificationEmail(txtbxNotificationEmail.getText());
        customerAccount.setNotificationPhone(txtbxNotificationPhone.getText());

        ClientEnhancedValidator clientValidator = ClientValidatorUtil.getInstance();
        if (!txtbxSurname.getText().trim().isEmpty()) {
            if (!clientValidator.validateField(customer, "surname", surnameElement)) {
                validated = false;
            }
        } else {
            surnameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.surname.null"));
            validated = false;
        }

        if (idNumberElement.isVisible() && ((!txtbxIdNumber.getText().trim().equals("")
                && !clientValidator.validateField(customer, "idNumber", idNumberElement))
                || !MeterMngClientUtils.validateUserInterfaceComponent(idNumberElement,
                        userInterfaceFields.get(UserInterfaceFormFields.ID_NUMBER), txtbxIdNumber))) {
            validated = false;
        }

        if (firstNamesElement.isVisible() && (!clientValidator.validateField(customer, "firstnames", firstNamesElement)
                || !MeterMngClientUtils.validateUserInterfaceComponent(firstNamesElement,
                        userInterfaceFields.get(UserInterfaceFormFields.FIRST_NAMES), txtbxFirstNames))) {
            validated = false;
        }

        if (initialsElement.isVisible() && (!clientValidator.validateField(customer, "initials", initialsElement)
                || !MeterMngClientUtils.validateUserInterfaceComponent(initialsElement,
                        userInterfaceFields.get(UserInterfaceFormFields.INITIALS), txtbxInitials))) {
            validated = false;
        }

        if (custTitleElement.isVisible()) {
            if (!clientValidator.validateField(customer, "title", custTitleElement)) {
                validated = false;
            } else {
                FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.TITLE);
                if (formField.isRequired() && titleListBox.getSelectedIndex() == 0) {
                    custTitleElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.required.field",
                            new String[] { formField.getName() }));
                }
            }
        }

        if (companyNameElement.isVisible()
                && (!clientValidator.validateField(customer, "companyName", companyNameElement)
                        || !MeterMngClientUtils.validateUserInterfaceComponent(companyNameElement,
                                userInterfaceFields.get(UserInterfaceFormFields.COMPANY_NAME), txtbxCompanyName))) {
            validated = false;
        }

        if (taxNumberElement.isVisible() && (!clientValidator.validateField(customer, "taxNum", taxNumberElement)
                || !MeterMngClientUtils.validateUserInterfaceComponent(taxNumberElement,
                        userInterfaceFields.get(UserInterfaceFormFields.TAX_NUMBER), txtbxTax))) {
            validated = false;
        }

        if (emailAddressElement.isVisible() && ((!txtbxEmail.getText().trim().equals("")
                && !clientValidator.validateField(customer, "email1", emailAddressElement))
                || !MeterMngClientUtils.validateUserInterfaceComponent(emailAddressElement,
                        userInterfaceFields.get(UserInterfaceFormFields.EMAIL_1), txtbxEmail))) {
            validated = false;
        }

        if (emailAddressElement2.isVisible() && ((!txtbxEmail2.getText().trim().equals("")
                && !clientValidator.validateField(customer, "email2", emailAddressElement2))
                || !MeterMngClientUtils.validateUserInterfaceComponent(emailAddressElement2,
                        userInterfaceFields.get(UserInterfaceFormFields.EMAIL_2), txtbxEmail2))) {
            validated = false;
        }

        if (phoneNumberElement.isVisible()) {
            FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.PHONE_1);
            String text = txtbxPhone.getText();
            boolean tempValidated = true;
            if (!text.trim().equals("")) {
                if (!clientValidator.validateField(customer, "phone1", phoneNumberElement)) {
                    tempValidated = false;
                }
                if (formField.getValidationRegex() == null && !ValidateUtil.isValidTelephoneNumber(text)) {
                    phoneNumberElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.phone"));
                    tempValidated = false;
                }
            }
            if (tempValidated
                    && !MeterMngClientUtils.validateUserInterfaceComponent(phoneNumberElement, formField, txtbxPhone)) {
                tempValidated = false;
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        if (phoneNumberElement2.isVisible()) {
            FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.PHONE_2);
            String text = txtbxPhone2.getText();
            boolean tempValidated = true;
            if (!text.trim().equals("")) {
                if (!clientValidator.validateField(customer, "phone2", phoneNumberElement2)) {
                    tempValidated = false;
                }
                if (formField.getValidationRegex() == null && !ValidateUtil.isValidTelephoneNumber(text)) {
                    phoneNumberElement2.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.phone2"));
                    tempValidated = false;
                }
            }
            if (tempValidated && !MeterMngClientUtils.validateUserInterfaceComponent(phoneNumberElement2, formField,
                    txtbxPhone2)) {
                tempValidated = false;
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        if (!ClientValidatorUtil.getInstance().validateField(customer, "mrid", mridComponent.getTxtbxMridElement())) {
            validated = false;
        }
        if (!mridComponent.validate()) {
            validated = false;
        }

        if (!txtbxAgreementRef.getText().trim().isEmpty()) {
            if (!clientValidator.validateField(customerAgreementData, "agreementRef", agreementRefElement)) {
                validated = false;
            }
            if(!MeterMngClientUtils.validateUserInterfaceComponent(agreementRefElement, 
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_AGR_REF), txtbxAgreementRef)) {
                validated = false;
            }
        } else {
            agreementRefElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.agreementref.null"));
            validated = false;
        }

        if (!txtbxCustRef.getText().trim().isEmpty()) {
            if (!clientValidator.validateField(customer, "customerReference", customerRefElement)) {
                validated = false;
            }
        } else {
            customerRefElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.customerreference.null"));
            validated = false;
        }

        if (startDateElement.isVisible()) {
            // Validate date input.
            Date installDt = dtbxStart.getValue();
            boolean tempValidated = true;
            String startDateText = dtbxStart.getTextBox().getValue();
            if (installDt != null) {
                if (!MeterMngClientUtils.isValidDate(startDateText)) {
                    startDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.startdate.invalid",
                            new String[] { FormatUtil.getInstance().getDateFormat() }));
                    tempValidated = false;
                } else if (installDt.after(new Date())) {
                    startDateElement
                            .showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.startdate.future"));
                    tempValidated = false;
                }
            } else if (!startDateText.isEmpty()) {
                startDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.startdate.invalid",
                        new String[] { FormatUtil.getInstance().getDateFormat() }));
                tempValidated = false;
            }
            if (tempValidated) {
                String valueText = null;
                if (installDt != null) {
                    valueText = installDt.toString();
                }
                String error = MeterMngClientUtils.getUserInterfaceComponentError(
                        userInterfaceFields.get(UserInterfaceFormFields.CUST_AGR_START_DATE), valueText);
                if (error != null) {
                    startDateElement.setErrorMsg(error);
                    tempValidated = false;
                }
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        if (!txtbxAccountName.getText().trim().isEmpty()) {
            if (!clientValidator.validateField(customerAccount, "accountName", accountNameElement)) {
                validated = false;
            }
            if(!MeterMngClientUtils.validateUserInterfaceComponent(accountNameElement, 
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NAME), txtbxAccountName)) {
                validated = false;
            }
        } else {
            accountNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.accountname.null"));
            validated = false;
        }

        if (!physicalLocationComponent.validate()) {
            validated = false;
        }

        if (physicalAddressPanel.isVisible()) {
            if (!physicalLocationComponent.validate()) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.erfnumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_ERF_NUMBER),
                    physicalLocationComponent.txtbxErfNumber)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.streetnumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_STREET_NUMBER),
                    physicalLocationComponent.txtbxStreetNumber)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.buildingNameElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_BUILDING_NAME),
                    physicalLocationComponent.txtbxBuildingName)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.suiteNumberElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_SUITE_NUMBER),
                    physicalLocationComponent.txtbxSuiteNumber)) {
                validated = false;
            }
            if (!physicalLocationComponent.validateUserInterfaceComponent(physicalLocationComponent.address1Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_1),
                    physicalLocationComponent.txtbxAddress1, physicalLocationComponent.address1ErrorLabel)) {
                validated = false;
            }
            if (!physicalLocationComponent.validateUserInterfaceComponent(physicalLocationComponent.address2Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_2),
                    physicalLocationComponent.txtbxAddress2, physicalLocationComponent.address2ErrorLabel)) {
                validated = false;
            }
            if (!physicalLocationComponent.validateUserInterfaceComponent(physicalLocationComponent.address3Panel,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_3),
                    physicalLocationComponent.txtbxAddress3, physicalLocationComponent.address3ErrorLabel)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.latitudeElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_LATITUDE),
                    physicalLocationComponent.txtbxLatitude)) {
                validated = false;
            }
            if (!MeterMngClientUtils.validateUserInterfaceComponent(physicalLocationComponent.longitudeElement,
                    userInterfaceFields.get(UserInterfaceFormFields.CUST_LONGITUDE),
                    physicalLocationComponent.txtbxLongitude)) {
                validated = false;
            }
        }

        if (lowBalanceThresholdElement.isVisible()
                && !MeterMngClientUtils.validateUserInterfaceComponent(lowBalanceThresholdElement,
                        userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_LOW_BAL_THRESHOLD),
                        txtbxlowThreshold.getCurrencyValueBox())) {
            validated = false;
        }

        if (notificationEmailElement.isVisible() && ((!txtbxNotificationEmail.getText().trim().equals("")
                && !clientValidator.validateField(customerAccount, "notificationEmail", notificationEmailElement))
                || !MeterMngClientUtils.validateUserInterfaceComponent(notificationEmailElement,
                        userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NOTIF_EMAIL),
                        txtbxNotificationEmail))) {
            validated = false;
        }

        if (notificationPhoneElement.isVisible()) {
            FormFields formField = userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NOTIF_PHONE);
            String text = txtbxNotificationPhone.getText();
            boolean tempValidated = true;
            if (!text.trim().equals("")) {
                if (!clientValidator.validateField(customerAccount, "notificationPhone", notificationPhoneElement)) {
                    validated = false;
                }
                if (formField.getValidationRegex() == null && !ValidateUtil.isValidTelephoneNumberList(text)) {
                    notificationPhoneElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.phone"));
                    validated = false;
                }
            }
            if (tempValidated && !MeterMngClientUtils.validateUserInterfaceComponent(notificationPhoneElement,
                    formField, txtbxNotificationPhone)) {
                tempValidated = false;
            }
            if (!tempValidated) {
                validated = false;
            }
        }

        if (userCustomFieldsComponentVisible) {
            if (!userCustomFieldsComponent.validateCustomFields()) {
                validated = false;
            }
        }

        return validated;
    }

    public void populateAuxAccountLookupListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            protected void onFailureClient() {
                mapDataToForm();
            }

            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                if (result != null) {
                    lstbxFreeIssue.setLookupItems(result);
                    mapDataToForm();
                }
            }
        };
        if (usagePointData != null && usagePointData.getCustomerAgreementId() != null) {
            if (clientFactory != null) {
                lstbxFreeIssue.clear();
                clientFactory.getLookupRpc().getAuxAccountsLookupList(usagePointData.getCustomerAgreementId(), lookupSvcAsyncCallback);
                lstbxFreeIssue.setVisible(true);
            } else {
                lstbxFreeIssue.setVisible(false);
            }
        }
    }

    public UsagePointData getUsagePointData() {
        return usagePointData;
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                customerDisclosurePanel.setWidth("100%");

            }
        }.schedule(100);

    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            customerDisclosurePanel.setOpen(isOpen);
        } else {
            customerDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    public void clearErrors() {
        surnameElement.clearErrorMsg();
        idNumberElement.clearErrorMsg();
        emailAddressElement.clearErrorMsg();
        firstNamesElement.clearErrorMsg();
        initialsElement.clearErrorMsg();
        custTitleElement.clearErrorMsg();
        companyNameElement.clearErrorMsg();
        taxNumberElement.clearErrorMsg();
        emailAddressElement.clearErrorMsg();
        emailAddressElement2.clearErrorMsg();
        phoneNumberElement.clearErrorMsg();
        phoneNumberElement2.clearErrorMsg();
        agreementRefElement.clearErrorMsg();
        startDateElement.clearErrorMsg();
        physicalLocationComponent.clearErrors();
        accountNameElement.clearErrorMsg();
        accountBalanceElement.clearErrorMsg();
        lowBalanceThresholdElement.clearErrorMsg();
        notificationEmailElement.clearErrorMsg();
        notificationPhoneElement.clearErrorMsg();
        billableElement.clearErrorMsg();
        customerRefElement.clearErrorMsg();
        mridComponent.clearErrorMsg();
        manageNotificationsDialogueBox.clearErrorMsg();
        if (userCustomFieldsComponentVisible) {
            userCustomFieldsComponent.clearErrorMessages();
        }
    }

    private void configureCustomFields() {
        boolean hasCustomFields = false;
        if (customFieldList != null && !customFieldList.isEmpty()) {
            hasCustomFields = userCustomFieldsComponent.configureCustomFields(customFieldList, "customer");
        }

        if (customFieldList == null || customFieldList.isEmpty() || !hasCustomFields) {
            removeUserCustomFieldsComponent();
            return;
        }

        if (hasCustomFields) {
            userCustomFieldsComponent.setVisible(true);
            userCustomFieldsComponentVisible = true;
        }
    }

    public void removeUserCustomFieldsComponent() {
        logger.info("Custom AppSettings are NULL or empty!!" );
        userCustomFieldsComponent.setVisible(false);
        userCustomFieldsComponentVisible = false;
    }

    public void addUserCustomFieldsComponent() {
        userCustomFieldsComponent.setVisible(true);
        userCustomFieldsComponentVisible = true;
        if (usagePointData.getCustomerAgreementData() != null) {
            mapUserCustomFields(usagePointData.getCustomerAgreementData().getCustomerData());
        }
    }

    public UserCustomFieldsComponent getUserCustomFieldsComponent() {
        return userCustomFieldsComponent;
    }

    public LocationComponent getLocationComponent() {
        return physicalLocationComponent;
    }

    @Override
    public void setHistoryVisibilityMap(Map<String, CustomFieldDto> result) {
    }

    public void addFieldHandlers() {
        titleListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxInitials.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxFirstNames.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxSurname.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxIdNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxCompanyName.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxTax.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEmail.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEmail2.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxPhone.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxPhone2.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxAgreementRef.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        dtbxStart.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        lstbxFreeIssue.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        chckbxBillable.addClickHandler(new FormDataClickHandler(hasDirtyData));
        txtbxAccountName.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxlowThreshold.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxNotificationEmail.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxNotificationPhone.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxCustRef.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    public void hideBillableCheckBox() {
        billableElement.setVisible(false);;
    }

    public void showBillableCheckBox() {
        billableElement.setVisible(true);
    }

    public void updateUserInterfaceComponentSettings(Map<String, FormFields> userInterfaceFields) {
        this.userInterfaceFields = userInterfaceFields;
        validateAgrRefRegex = userInterfaceFields.get(UserInterfaceFormFields.CUST_AGR_REF).getValidationRegex() != null;
        validateAccNameRegex = userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NAME).getValidationRegex() != null;
        // After the async callback for userInterfaceFields we can safely generate these fields knowing the value of the regex.
        generateAccountName();
        generateAgreementRef();
        MeterMngClientUtils.setUserInterfaceComponentPreferences(userInterfaceFields.get(UserInterfaceFormFields.TITLE),
                custTitleElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.INITIALS), initialsElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.FIRST_NAMES), firstNamesElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.ID_NUMBER), idNumberElement);

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.COMPANY_NAME), companyNameElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.TAX_NUMBER), taxNumberElement);
        companyNameTaxNumberPanel.setVisible(companyNameElement.isVisible() || taxNumberElement.isVisible());

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.EMAIL_1), emailAddressElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.EMAIL_2), emailAddressElement2);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.PHONE_1), phoneNumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.PHONE_2), phoneNumberElement2);
        contactDetailsPanel.setVisible(emailAddressElement.isVisible() || emailAddressElement2.isVisible()
                || phoneNumberElement.isVisible() || phoneNumberElement2.isVisible());

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_AGR_START_DATE), startDateElement);

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_LOW_BAL_THRESHOLD),
                lowBalanceThresholdElement);
        lowBalanceThresholdPanel.setVisible(lowBalanceThresholdElement.isVisible());

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NOTIF_EMAIL), notificationEmailElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ACC_NOTIF_PHONE), notificationPhoneElement);
        notificationPanel.setVisible(notificationEmailElement.isVisible() || notificationPhoneElement.isVisible());

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ERF_NUMBER),
                physicalLocationComponent.erfnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_STREET_NUMBER),
                physicalLocationComponent.streetnumberElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_BUILDING_NAME),
                physicalLocationComponent.buildingNameElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_SUITE_NUMBER),
                physicalLocationComponent.suiteNumberElement);

        physicalLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_1),
                physicalLocationComponent.address1Panel, physicalLocationComponent.address1RequiredLabel,
                physicalLocationComponent.address1ErrorLabel);
        physicalLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_2),
                physicalLocationComponent.address2Panel, physicalLocationComponent.address2RequiredLabel,
                physicalLocationComponent.address2ErrorLabel);
        physicalLocationComponent.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_ADDRESS_3),
                physicalLocationComponent.address3Panel, physicalLocationComponent.address3RequiredLabel,
                physicalLocationComponent.address3ErrorLabel);

        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_LATITUDE),
                physicalLocationComponent.latitudeElement);
        MeterMngClientUtils.setUserInterfaceComponentPreferences(
                userInterfaceFields.get(UserInterfaceFormFields.CUST_LONGITUDE),
                physicalLocationComponent.longitudeElement);

        physicalAddressPanel.setVisible(physicalLocationComponent.handleUserInterfaceComponentGroups());
        populateTitleListBox();
    }

    public Label getLblAssign() {
        return lblAssign;
    }

    public Button getBtnSave() {
        return btnSave;
    }
}
