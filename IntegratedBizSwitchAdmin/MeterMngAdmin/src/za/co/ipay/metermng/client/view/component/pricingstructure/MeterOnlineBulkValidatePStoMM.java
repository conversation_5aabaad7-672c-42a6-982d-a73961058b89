package za.co.ipay.metermng.client.view.component.pricingstructure;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.workspace.meter.onlinebulk.MeterOnlineBulkWorkspaceView;
import za.co.ipay.metermng.shared.dto.PSDto;

import java.util.List;
import java.util.logging.Logger;

public class MeterOnlineBulkValidatePStoMM extends AbstractValidatePStoMM {
    
    private final MeterOnlineBulkWorkspaceView parent;
    private String action;

    public MeterOnlineBulkValidatePStoMM(MeterOnlineBulkWorkspaceView parent) {
        super();
        this.parent = parent;
    }

    public void isregReadPsSameBillingDetsAsMeterModel(ClientFactory clientFactory, List<PSDto> pricingStructureDtos,
            Long mdcId, String userName, Logger logger, String action) {
        this.action = action;
        super.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pricingStructureDtos, mdcId, userName, logger, "MeterOnlineBulkWorkspaceView");
    }
    
    @Override
    public void errorNoTariff() {
        if (action.equals("save")) {
            parent.saveErrorOrNonMatchPsToMm();
        } else {
            parent.updateErrorOrNonMatchPsToMm();
        }
    }

    @Override
    public void noneMatchContinue() {
        if (action.equals("save")) {
            parent.saveErrorOrNonMatchPsToMm();
        } else {
            parent.updateErrorOrNonMatchPsToMm();
        }
    }

    @Override
    public void partialMatchConfirmContinue() {
        if (action.equals("save")) {
            parent.save5ConfirmSendFreeTokenBySms();
        } else {
            parent.updatePassTocheckChannelInfoB4Save();
        }
    }

    @Override
    public void partialMatchDenyContinue() {
        if (action.equals("save")) {
            parent.savePartialMatchDenyContinue();
        } else {
            parent.resetMeterModelAndPricingStructureSelection();
        }
    }

    @Override
    public void exactMatchOrNoDataContinue() {
        if (action.equals("save")) {
            parent.save5ConfirmSendFreeTokenBySms();
        } else {
            parent.updatePassTocheckChannelInfoB4Save();
        }
    }

}
