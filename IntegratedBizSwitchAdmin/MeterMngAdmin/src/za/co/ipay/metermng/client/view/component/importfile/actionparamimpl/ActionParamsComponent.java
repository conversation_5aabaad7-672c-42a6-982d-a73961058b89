package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl;

import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;

public class ActionParamsComponent extends BaseComponent {
    private BulkParamRecord bulkParamRecord;
    private String errorMessage;
  
    public ActionParamsComponent() {
        super();
    }

    public ActionParamsComponent(String errorMessage) {
        super();
        this.errorMessage = errorMessage;
    }

    public void mapDataToForm() {
    }
    
    public boolean validateForm() {
        return false;
    }
    
    public BulkParamRecord mapFormToData() {
        return bulkParamRecord;
    }

    public boolean checkDirtyData() {
        return false;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
