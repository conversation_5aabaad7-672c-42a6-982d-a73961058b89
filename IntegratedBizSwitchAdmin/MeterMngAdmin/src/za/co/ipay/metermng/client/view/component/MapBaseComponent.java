package za.co.ipay.metermng.client.view.component;

import java.util.List;

import com.google.gwt.maps.client.MapOptions;
import com.google.gwt.maps.client.MapWidget;
import com.google.gwt.maps.client.base.LatLng;
import com.google.gwt.maps.client.base.LatLngBounds;
import com.google.gwt.user.client.ui.SimplePanel;

import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;

public abstract class MapBaseComponent extends BaseComponent {

    protected MapWidget map;

    protected void loadMapPanel(SimplePanel mapPanel, LatLng mapCoordinates, List<GisMetadata> gisMetadata, String defaultLabel) {
        mapPanel.clear();
        if (clientFactory.isGoogleMapsReady()) {
            if (mapCoordinates != null || !gisMetadata.isEmpty()) {
                MapOptions mapOptions = MeterMngClientUtils.createMapOptionsInstance();
                map = new MapWidget(mapOptions);
                map.setSize("100%", "100%");
                map.setZoom(MeterMngClientUtils.DEFAULT_ZOOM);
                LatLngBounds bounds = null;
                if (mapCoordinates != null) {
                    mapOptions.setCenter(mapCoordinates);
                    map.setCenter(mapCoordinates);
                    bounds = LatLngBounds.newInstance(mapCoordinates, mapCoordinates);
                }

                // Add a marker
                if (mapCoordinates != null) {
                    MeterMngClientUtils.addMarkerToMapAtLatLng(map, mapCoordinates, new MeterMngClientUtils.DefaultClickMapHandler(map, defaultLabel));
                }

                for (final GisMetadata entry : gisMetadata) {
                    // Add more markers
                    LatLng latLng = LatLng.newInstance(entry.getLat(), entry.getLon());
                    if (bounds != null) {
                        bounds.extend(latLng);
                    } else if (mapCoordinates == null) {
                        bounds = LatLngBounds.newInstance(latLng, latLng);
                        mapOptions.setCenter(latLng);
                        map.setCenter(latLng);
                    }
                    MeterMngClientUtils.addMarkerToMapAtLatLng(map, latLng, new MeterMngClientUtils.DefaultClickMapHandler(map, "GroupId" + entry.getDesc()));
                }
                if (bounds != null) {
                    map.fitBounds(bounds);
                }
            }
        }
        if (map != null) {
            mapPanel.add(map);
            map.getParent().setSize("350px", "330px");
            map.triggerResize();
        }
    }
}
