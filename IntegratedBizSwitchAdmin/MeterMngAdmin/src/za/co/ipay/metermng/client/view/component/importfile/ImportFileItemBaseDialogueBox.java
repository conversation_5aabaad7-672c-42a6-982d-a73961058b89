package za.co.ipay.metermng.client.view.component.importfile;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.cell.client.TextInputCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.json.client.JSONNull;
import com.google.gwt.json.client.JSONNumber;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.CellPreviewEvent.Handler;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.registerreading.RegisterReadingImportRecord;

public class ImportFileItemBaseDialogueBox extends DialogBox {

    @UiField ScrollPanel scrollPanel;
    @UiField(provided = true) CellTable<ImportRecordField> table;

    @UiField Button updateBtn;
    @UiField Button cancelBtn;

    protected List<ImportRecordField> dataList = new ArrayList<>();
    protected ListDataProvider<ImportRecordField> dataProvider = new ListDataProvider<>();
    private static final int DEFAULT_PAGE_SIZE = 17;

    protected ImportFileItemDto importFileItemDto;
    protected boolean isDirtyData = false;

    protected ClientFactory clientFactory;

    protected DateTimeFormat df = DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat());
    private static Logger logger = Logger.getLogger(ImportFileItemBaseDialogueBox.class.getName());

    private static ImportFileItemBaseDialogueBoxUiBinder uiBinder = GWT.create(ImportFileItemBaseDialogueBoxUiBinder.class);
    private ImportFileItemView importFileItemView;

    interface ImportFileItemBaseDialogueBoxUiBinder extends UiBinder<Widget, ImportFileItemBaseDialogueBox> {
    }

    public ImportFileItemBaseDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        this.clientFactory = clientFactory;
        this.importFileItemView = importFileItemView;
        table = new CellTable<ImportRecordField>(DEFAULT_PAGE_SIZE,
                ResourcesFactoryUtil.getInstance().getCellTableResources());
        setWidget(uiBinder.createAndBindUi(this));
        createTable();
        Messages messagesInstance = MessagesUtil.getInstance();
        updateBtn.setText(messagesInstance.getMessage("button.update"));
        cancelBtn.setText(messagesInstance.getMessage("button.cancel"));
    }

    private void createTable() {
        TextColumn<ImportRecordField> fieldNameCol = new TextColumn<ImportRecordField>() {
            @Override
            public String getValue(ImportRecordField object) {
                return object.getFieldname();
            }
        };

        Column<ImportRecordField, String> fieldValueCol;
        String importFileTypeName = importFileItemView.getImportFileTypeName();
        if (importFileTypeName != null && MeterMngStatics.FILETYPE_BULK_TARIFF_UPDATER.equals(importFileTypeName)) {
            fieldValueCol = new Column<ImportRecordField, String>(
                    new TextAreaInputCellWithWidth()) {
                @Override
                public String getValue(ImportRecordField object) {
                    return object.getFieldValue();
                }
            };

        } else {
            fieldValueCol = new Column<ImportRecordField, String>(
                    new TextInputCellWithWidth()) {
                @Override
                public String getValue(ImportRecordField object) {
                    return object.getFieldValue();
                }
            };
        }
        fieldValueCol.setFieldUpdater(new FieldUpdater<ImportRecordField, String>() {
            @Override
            public void update(int index, ImportRecordField object, String value) {
                isDirtyData = true;
                object.setFieldValue(value);
                dataList.set(index, object);
                setDataProviderList();
                table.redraw();
            }
        });

        table.addCellPreviewHandler(new Handler<ImportRecordField>() {
            @Override
            public void onCellPreview(CellPreviewEvent<ImportRecordField> event) {
                if ("mouseover".equals(event.getNativeEvent().getType())) {
                    ImportRecordField cellElement = event.getValue();
                     if (cellElement.getFieldname().equals("calcContents")) {

                    table.getRowElement(event.getIndex()).getCells().getItem(0).setTitle(
                            "Note: When editing calc contents, maintain the JSON double quotes.");
                     }
                  }
            }
        });

        table.addColumn(fieldNameCol, "");
        table.addColumn(fieldValueCol, "");
        dataProvider.addDataDisplay(table);
    }

    private void setDataProviderList() {
        dataProvider.setList(dataList);
        table.setRowCount(dataProvider.getList().size(), true);
        table.setPageSize(dataProvider.getList().size());
        dataProvider.refresh();

        //Set scrollpanel height - but only makes sense after Dom created, so defer
        Scheduler.get().scheduleDeferred(new ScheduledCommand() {
            @Override
            public void execute() {
                checkHeightForScrollpanel();
            }
          });
    }

    private void checkHeightForScrollpanel() {
        //calc pagesize for scrollpanel
        int pageHeight = Window.getClientHeight();
        int tableHeight = table.getOffsetHeight();

        if (tableHeight >  pageHeight) {
            scrollPanel.setHeight(pageHeight - 300 + "px");      //200 for top position & 100 for buttons at bottom
        } else {
            scrollPanel.setHeight(tableHeight + "px");
        }
    }

    public void loadItem(ImportFileItemDto itemDto) {
        this.importFileItemDto = itemDto;
        createDataList(itemDto);
        setDataProviderList();
    }

    @UiHandler("updateBtn")
    public void updateRecord(ClickEvent e) {
        checkDirtyData();

        if (isDirtyData) {
            enableButtons(false);

            final ImportFileItemDto importFileItem = new ImportFileItemDto();
            importFileItem.setImportFileItem(importFileItemDto.getImportFileItem());
            prepareImportFileItem(importFileItem);
            //for Register Readings import: display the error from the manual update
            if (importFileItem.getGenericImportRecord() != null
                    && importFileItem.getGenericImportRecord() instanceof RegisterReadingImportRecord
                    && importFileItem.getImportFileItemImportComment() != null
                    && !importFileItem.getImportFileItemImportComment().isEmpty()) {
                Dialogs.displayErrorMessage(importFileItem.getImportFileItemImportComment());
                enableButtons(true);
                return;
            }

            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getImportFileDataRpc().updateImportFileItem(importFileItem, new ClientCallback<String>() {
                        @Override
                        public void onSuccess(String result) {
                            enableButtons(true);
                            isDirtyData = false;
                            displayUpdateMessage();
                            importFileItemDto.getImportFileItem().setItemData(result);
                            updateParentRow();
                            importFileItemView.refreshTable();
                            hideThis();
                        }

                        @Override
                        public void onFailureClient(Throwable caught) {
                            logger.info(
                                    "UPDATE ITEM ERROR: throwable = " + caught.getCause() + "  stack= " + caught.getMessage());
                            enableButtons(true);
                        }
                    });
                    isDirtyData = false;
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        } else {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("import.edit.item.update.non"),
                    MediaResourceUtil.getInstance().getInformationIcon());
        }
    }

    private void enableButtons(boolean enable) {
        updateBtn.setEnabled(enable);
        cancelBtn.setEnabled(enable);
    }

    protected void displayUpdateMessage() {
        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("import.edit.generic.update.success"),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    protected void updateParentRow() {
        importFileItemDto.setImportFileRecordStr(getItemData());
    }

    @UiHandler("cancelBtn")
    public void cancelChanges(ClickEvent e) {
        checkDirtyData();

        if (isDirtyData) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hideThis();
                    }
                }
            });
        } else {
            hideThis();
        }
    }

    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        dataList.clear();
        JSONObject importObject = JSONParser.parseStrict(itemDto.getImportFileRecordStr()).isObject();
        for (String str : importObject.keySet()) {
            JSONValue jsonValue = importObject.get(str);
            JSONNumber jsonNumber = jsonValue.isNumber();
            String fieldValue;
            String fieldType;
            if (jsonNumber == null) {
                if (jsonValue.isNull() == null) {
                    fieldValue = jsonValue.toString();
                    fieldType = JSONString.class.getSimpleName();
                } else {
                    fieldValue = "";
                    fieldType = JSONNull.class.getSimpleName();
                }
            } else {
                fieldValue = jsonNumber.toString();
                fieldType = JSONNumber.class.getSimpleName();
            }
            dataList.add(new ImportRecordField(str, fieldValue, fieldType));
        }
        return dataList;
    }

    protected void checkDirtyData() {
    }

    public void clearThis() {
        isDirtyData = false;
        dataProvider.getList().clear();
        table.setRowCount(dataProvider.getList().size(), true);
        dataProvider.refresh();
    }

    private void hideThis() {
        clearThis();
        this.hide();
    }

    private String getItemData() {
        JSONObject jsonObj = new JSONObject();
        for (ImportRecordField importRecordField : dataList) {
            JSONValue jsonValue;
            String fieldValue = importRecordField.getFieldValue();
            switch (importRecordField.getFieldType()) {
            case "JSONNumber":
                if(fieldValue != null && !fieldValue.isEmpty()) {
                    jsonValue = new JSONNumber(Double.parseDouble(fieldValue));
                } else {
                    jsonValue = JSONNull.getInstance();
                }

                break;
            case "JSONNull":
                if (fieldValue.isEmpty()) {
                    jsonValue = JSONNull.getInstance();
                } else {
                    jsonValue = new JSONString(fieldValue);
                }
                break;
            default:
                jsonValue = new JSONString(fieldValue);
            }
            jsonObj.put(importRecordField.getFieldname(), jsonValue);
        }
        return jsonObj.toString();
    }

    protected void prepareImportFileItem(ImportFileItemDto importFileItem) {
        importFileItem.setImportFileRecordStr(getItemData());
    }

    // ---------------------------------------------------------------------------------------------------
    protected class ImportRecordField {
        private String fieldname;
        private String fieldValue;
        private String fieldType;

        public ImportRecordField(String fieldname, String fieldValue) {
            this(fieldname, fieldValue, null);
        }

        public ImportRecordField(String fieldname, String fieldValue, String fieldType) {
            //for Tariff bulk updater, CalcContents field is itself a JSON Object. KEEP the quotes!
            super();
            this.fieldname = fieldname.replaceAll("\"", "");
            if (fieldType != null && fieldType.equals(JSONObject.class.getSimpleName())) {
                this.fieldValue = fieldValue;
            } else {
                this.fieldValue = fieldValue.replaceAll("\"", "");
            }
            this.fieldType = fieldType;
        }

        public String getFieldname() {
            return fieldname;
        }

        public String getFieldValue() {
            return fieldValue;
        }

        public void setFieldValue(String fieldValue) {
            this.fieldValue = fieldValue;
        }

        public String getFieldType() {
            return fieldType;
        }

        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }
    }

    // --------------------------------------------------------------------------------------------------
    static class TextInputCellWithWidth extends TextInputCell {
        private static Template template;

        interface Template extends SafeHtmlTemplates {
            // {0}, {1}, {2} relate to value, size, style
            @Template("<input type=\"text\" value=\"{0}\" tabindex=\"-1\" style=\"{1}\"></input>")
            SafeHtml input(String value, SafeStyles style);
        }

        public TextInputCellWithWidth() {
            template = GWT.create(Template.class);
        }

        @Override
        public void render(Context context, String value, SafeHtmlBuilder sb) {
            // Get the view data.
            Object key = context.getKey();
            ViewData viewData = getViewData(key);
            if (viewData != null && viewData.getCurrentValue().equals(value)) {
                clearViewData(key);
                viewData = null;
            }

            String s = (viewData != null) ? viewData.getCurrentValue() : value;
            SafeStyles styles = SafeStylesUtils.forWidth(270, Unit.PX);

            if (s != null) {
                // this is where we set value, size, style
                sb.append(template.input(s, styles));
            } else {
                sb.appendHtmlConstant("<input type=\"text\" tabindex=\"-1\"></input>");
            }
        }
    }
    // --------------------------------------------------------------------------------------------------
    static class TextAreaInputCellWithWidth extends TextInputCell {
        private static Template template;

        interface Template extends SafeHtmlTemplates {
            // {0}, {1}, {2} relate to value, size, style
            //@Template("<input type=\"text\" value=\"{0}\" tabindex=\"-1\" style=\"{1}\"></input>")
            //@Template("<textarea tabindex=\"-1\" rows=\"{1}\" cols=\"{2}\" >{0}</textarea>")
            @Template("<textarea tabindex=\"-1\" style=\"{1}\" >{0}</textarea>")
            SafeHtml input(String value, SafeStyles style);
        }

        public TextAreaInputCellWithWidth() {
            template = GWT.create(Template.class);
        }

        @Override
        public void render(Context context, String value, SafeHtmlBuilder sb) {
            // Get the view data.
            Object key = context.getKey();
            ViewData viewData = getViewData(key);
            if (viewData != null && viewData.getCurrentValue().equals(value)) {
                clearViewData(key);
                viewData = null;
            }

            String s = (viewData != null) ? viewData.getCurrentValue() : value;
            SafeStyles styles = SafeStylesUtils.forWidth(270, Unit.PX);

            if (s != null) {
                // this is where we set value, size, style
                sb.append(template.input(s, styles));
            } else {
                sb.appendHtmlConstant("<input type=\"text\" tabindex=\"-1\"></input>"
                        + "<textarea tabindex=\"-1\" ></textarea>");
            }
        }
    }
}
