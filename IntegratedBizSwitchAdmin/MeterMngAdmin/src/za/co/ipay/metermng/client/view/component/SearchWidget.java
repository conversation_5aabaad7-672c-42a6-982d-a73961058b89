package za.co.ipay.metermng.client.view.component;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.handler.EnterKeyHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.Message;
import za.co.ipay.metermng.client.event.CustomerSearchEvent;
import za.co.ipay.metermng.client.event.MeterSearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.CustomerPlace;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.CustomerSuggestion;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.dom.client.KeyPressEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class SearchWidget extends BaseComponent {
    
    private static Logger logger = Logger.getLogger(SearchWidget.class.getName());
    private static SearchWidgetUiBinder uiBinder = GWT.create(SearchWidgetUiBinder.class);

    interface SearchWidgetUiBinder extends UiBinder<Widget, SearchWidget> {
    }
    
    private ClientFactory clientFactory;
    @UiField FlowPanel searchPanel;
    @UiField IpayListBox lstbxSearchType;
    @UiField FlowPanel metersearch;
    @UiField TextBox txtbxMeterNumber;
    @UiField FlowPanel customersearch;
    @UiField(provided=true) SuggestBox suggestBoxCustomer;
    @UiField Message errormessage;
    @UiField Image imgSearchM;
    
    public SearchWidget(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.suggestBoxCustomer = new SuggestBox(clientFactory.getCustomerSuggestionsOracle());
        initWidget(uiBinder.createAndBindUi(this));
        imgSearchM.setResource(MediaResourceUtil.getInstance().getSearchImage());
        errormessage.setVisible(false);
        txtbxMeterNumber.addKeyDownHandler(new EnterKeyHandler() {            
            @Override
            public void enterKeyDown(KeyDownEvent event) {
                search();
            }
        });
        lstbxSearchType.addItem(MessagesUtil.getInstance().getMessage("meter.title"));
        lstbxSearchType.addItem(MessagesUtil.getInstance().getMessage("customer.title"));
        lstbxSearchType.setItemSelected(0, true);
    }
    
    private void search() {
        String meterNum = txtbxMeterNumber.getText().trim();
        clientFactory.getEventBus().fireEvent(new MeterSearchEvent(new MeterPlace(meterNum, false)));
    }

    @UiHandler("lstbxSearchType")
    void handleSearchType(ChangeEvent e) {
        metersearch.setVisible(lstbxSearchType.getSelectedIndex()==0);
        customersearch.setVisible(!(lstbxSearchType.getSelectedIndex()==0));
    }
    
    @UiHandler("imgSearchM")
    void handleSearchButton(ClickEvent e) {
        search();
    }
    
    @UiHandler("txtbxMeterNumber")
    void handleSearchButton(KeyPressEvent e) {
        if (e.getNativeEvent().getKeyCode() == KeyCodes.KEY_ENTER) {
            search();
        }
    }
    
    @UiHandler("suggestBoxCustomer") 
    void handleSuggestBox(SelectionEvent<com.google.gwt.user.client.ui.SuggestOracle.Suggestion> se) {
        CustomerSuggestion customer = (CustomerSuggestion)se.getSelectedItem();
        logger.info("SuggestBox customer=" + customer);
        clientFactory.getEventBus().fireEvent(new CustomerSearchEvent(new CustomerPlace(customer.getCustomerId(), false)));
        //suggestBoxCustomer.setText("");
    }
    
    public void setInline() {
        searchPanel.setStyleName("horizontal");
        lstbxSearchType.setStyleName("horizontal");
        metersearch.setStyleName("horizontal");
        customersearch.setStyleName("horizontal");
    }     
}
