package za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupDisplay;

import java.util.ArrayList;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.CellPanel;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

public class SingleUpGroupDisplayWidget extends BaseComponent {
    private boolean horizontalLayout;
    @UiField VerticalPanel wrapperPanel;
    private VerticalPanel captionPanel;
    private CellPanel mainPanel;    
    private CheckBox copyGroupCaptionLabel;
    private String groupTypeName;
    private Long groupTypeId;

    private static Logger logger = Logger.getLogger(SingleUpGroupDisplayWidget.class.getName());
    
    private static SingleUpGroupDisplayWidgetUiBinder uiBinder = GWT.create(SingleUpGroupDisplayWidgetUiBinder.class);    
    interface SingleUpGroupDisplayWidgetUiBinder extends UiBinder<Widget, SingleUpGroupDisplayWidget> {
    }

    public SingleUpGroupDisplayWidget(UpGenGroupLinkDataNames upGroupNames) {
        initWidget(uiBinder.createAndBindUi(this));
        captionPanel = new VerticalPanel();
        this.groupTypeName = upGroupNames.getGroupTypeName();
        this.groupTypeId = upGroupNames.getGroupTypeId();
        copyGroupCaptionLabel = new CheckBox(this.groupTypeName + ":");
        copyGroupCaptionLabel.setValue(Boolean.TRUE);
        copyGroupCaptionLabel.addStyleName("pseudoCaptionLabel");
        captionPanel.add(copyGroupCaptionLabel);
        
        captionPanel.addStyleName("pseudoCaptionPanel");
        wrapperPanel.add(captionPanel);  
        if (horizontalLayout) {
            mainPanel = new HorizontalPanel();
        } else {
            mainPanel = new VerticalPanel();
        }
        captionPanel.add(mainPanel);
        
        ArrayList<String> hierarchyNamesList = upGroupNames.getHierarchyNamesList();
        ArrayList<String> depthNamesList = upGroupNames.getDepthNamesList();
        
        for (int i=0; i<hierarchyNamesList.size(); i++) {
            FormRowPanel row = new FormRowPanel();
            FormElement element = new FormElement();
            element.setLabelText(hierarchyNamesList.get(i));
            TextBox txtBx = new TextBox();
            txtBx.setText(depthNamesList.get(i));
            txtBx.setEnabled(false);
            element.add(txtBx);
            row.add(element);
            mainPanel.add(row);
        }
    }
    
    public boolean isSelected() {
        return copyGroupCaptionLabel.getValue();
    }
    
    public Long getGroupTypeId() {
        return groupTypeId;
    }
    

}
