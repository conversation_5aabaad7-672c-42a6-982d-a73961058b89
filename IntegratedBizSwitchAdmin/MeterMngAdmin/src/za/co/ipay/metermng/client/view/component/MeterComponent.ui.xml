<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p2="urn:import:za.co.ipay.metermng.client.view.component"
             xmlns:p4="urn:import:za.co.ipay.metermng.client.view.component.usercustomfields">
	<ui:style>

	</ui:style>

  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
    <g:HTMLPanel>
	<g:DisclosurePanel open="true" styleName="gwt-DisclosurePanel-component" ui:field="meterDisclosurePanel" debugId="meterDisclosurePanel">
        <g:customHeader>
            <g:FlowPanel debugId="meterDisclosurePanel" styleName="gwt-DisclosurePanel-component .header" width="100%">
                <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                        <g:Image styleName="horizontalFlow" ui:field="openorclosearrow"/>
                        <g:Image ui:field="meterImage" />
                        <g:Label debugId="meterDisclosurePanelHeader" styleName="gwt-DisclosurePanel-component .header" width="" ui:field="headerLabel" text="{msg.getMeterTitle}:" horizontalAlignment="ALIGN_LEFT" />
                </g:HorizontalPanel>
            </g:FlowPanel>
        </g:customHeader>
        <g:FlowPanel>
        <g:FlowPanel styleName="disclosureContent" ui:field="contentPanel">
          <g:FlowPanel styleName="formElementsPanel" debugId="meterPanel">
            <ipay:FormRowPanel>
                <ipay:FormElement ui:field="meterModelElement" helpMsg="{msg.getSelectMeterModelHelp}" labelText="{msg.getSelectMeterModel}:" required="true">
                    <p3:IpayListBox visibleItemCount="1" ui:field="lstbxMeterModel" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="meterModelBox" />
                </ipay:FormElement>
                <ipay:FormElement ui:field="replaceLinkElement">
                    <g:Label debugId="assignMeterLink" text="{msg.getMeterReplace}" ui:field="lblReplace" styleName="gwt-Label-iPayLink"/>
                </ipay:FormElement>
                <ipay:FormElement ui:field="removeLinkElement" helpMsg="{msg.getMeterRemoveHelp}">
                    <g:Label debugId="removeMeterLink" text="{msg.getMeterRemove}" ui:field="lblRemove" styleName="gwt-Label-iPayLink"/>
                </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormRowPanel ui:field="meterPhasePanel" visible="false">
                <ipay:FormElement labelText="{msg.getMeterSelectMeterPhase}:">
                    <g:TextBox debugId="meterPhaseBox" ui:field="txtbxMeterPhase" styleName="gwt-TextBox" enabled="false"/>
                </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormRowPanel >
	            <ipay:FormElement debugId="meterNumElement" ui:field="meterNumberElement" required="true" helpMsg="{msg.getMeterNumberHelp}" labelText="{msg.getMeterNumber}:">
	                <g:TextBox debugId="meterNumBox" ui:field="txtbxMeterNumber" styleName="gwt-TextBox" visibleLength="15" />
	            </ipay:FormElement>
	            <ipay:FormElement ui:field="meterSerialNumElement" helpMsg="{msg.getMeterSerialNumberHelp}" labelText="{msg.getMeterSerialNumber}:" >
	                <g:TextBox debugId="meterSerialBox" ui:field="txtbxSerialNumber" styleName="gwt-TextBox" visibleLength="15" />
	            </ipay:FormElement>
            </ipay:FormRowPanel>

            <p2:MridComponent ui:field="mridComponent"></p2:MridComponent>
            
            <ipay:FormRowPanel ui:field="breakerIdPanel" visible="false">
                <ipay:FormElement ui:field="breakerIdElement" debugId="breakerIdElement" required="true" helpMsg="{msg.getMeterBreakerIdHelp}" labelText="{msg.getMeterBreakerId}:">
                    <g:TextBox debugId="txtbxBreakerId" ui:field="txtbxBreakerId" styleName="gwt-TextBox" visibleLength="20"/>
                </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormRowPanel ui:field="encryptionKeyPanel" visible="false">
                <ipay:FormElement ui:field="encryptionKeyElement" debugId="encryptionKeyElement" required="true" helpMsg="{msg.getMeterEncryptionKeyHelp}" labelText="{msg.getMeterEncryptionKey}:">
                    <g:TextBox debugId="txtbxEncryptionKey" ui:field="txtbxEncryptionKey" styleName="gwt-TextBox" visibleLength="20"/>
                </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormGroupPanel labelText="{msg.getMeterStsInfo}" ui:field="stsContainer">
           	        <ipay:FormRowPanel >
           	            <ipay:FormElement ui:field="algCodeElement" required="true" helpMsg="{msg.getMeterAlgorithmCodeHelp}" labelText="{msg.getMeterAlgorithmCode}">
                            <p3:IpayListBox debugId="meterAlgBox" visibleItemCount="1" ui:field="lstbxAlgCode" styleName="gwt-ListBox-ipay" multipleSelect="false"  />
                        </ipay:FormElement>
                        <ipay:FormElement ui:field="tTCodeElement" required="true" helpMsg="{msg.getMeterTokenTechCodeHelp}" labelText="{msg.getMeterTokenTechCode}">
                            <p3:IpayListBox debugId="meterTokenTechBox" visibleItemCount="1" ui:field="lstbxTtCode" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                        </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel>
                      <ipay:FormElement ui:field="currSGCElement" required="true" helpMsg="{msg.getMeterSupplyGroupCodeHelp}" labelText="{msg.getMeterSupplyGroupCode}">
                          <p3:IpayListBox debugId="meterSupplyGroupBox" visibleItemCount="1" ui:field="lstbxSgKrn" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel>
                      <ipay:FormElement ui:field="currTIElement" required="true" helpMsg="{msg.getMeterTariffIndexHelp}" labelText="{msg.getMeterTariffIndex}">
                          <g:TextBox debugId="meterTariffIndexBox" ui:field="txtbxCurrTI" styleName="gwt-TextBox" visibleLength="3" maxLength="2"/>
                      </ipay:FormElement>
                      <ipay:FormElement ui:field="lblBaseDate" labelText="{msg.getBaseDateLabel}" helpMsg="{msg.getBaseDateLabelHelp}">
                          <g:Label ui:field="baseDateLabel"></g:Label>
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel visible="false" ui:field="chckbxThreeTokensFormRow">
                      <ipay:FormElement ui:field="chckbxThreeTokensElement" helpMsg="{msg.getMeterThreeTokensHelp}" >
                        <g:CheckBox debugId="meterThreeTokensChckbx" text="{msg.getMeterThreeTokens}" checked="false" ui:field="chckbxThreeTokens" />
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel visible="false" ui:field="generateKeyChangeFormRow">
                      <ipay:FormElement ui:field="generateKeyChangeElement" helpMsg="{msg.getMeterGenerateKeyChangeHelp}" labelText="{msg.getMeterGenerateKeyChange}">
                          <p3:IpayListBox debugId="meterKeyChangeBox" ui:field="lstbxGenerateKeyChange" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                    <g:Label ui:field="pendingkeychange" visible="false" styleName="gwt-Label-bold-left" />
             </ipay:FormGroupPanel>
              <ipay:FormGroupPanel labelText="{msg.getMeterPowerLimitContainer}" ui:field="powerLimitContainer">
                  <ipay:FormRowPanel>
                      <ipay:FormElement ui:field="powerLimitElement" helpMsg="{msg.getMeterPowerLimitHelp}" labelText="{msg.getMeterPowerLimit}:">
                          <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                              <p3:IpayListBox ui:field="powerLimitListBox" visibleItemCount="1" styleName="gwt-ListBox-ipay" width="100px" debugId="powerLimitListBox"/>
                          </g:HorizontalPanel>
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel visible="false" ui:field="generatePowerLimitFormRow">
                      <ipay:FormElement ui:field="generatePowerLimitTokenElement" helpMsg="{msg.getMeterGeneratePowerLimitTokenHelp}" labelText="{msg.getMeterGeneratePowerLimitToken}">
                          <p3:IpayListBox debugId="generatePowerLimitToken" ui:field="generatePowerLimitTokenListBox" visibleItemCount="1" styleName="gwt-ListBox-ipay" multipleSelect="false"/>
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
              </ipay:FormGroupPanel>
              <p4:UserCustomFieldsComponent ui:field="userCustomFieldsComponent" width="100%" visible="false"/>
              <ipay:FormGroupPanel labelText="{msg.getMeterCentianHeader}" ui:field="centianContainer" visible="false" width="100%">
                    <g:FlowPanel>
                        <g:Label text="{msg.getMeterCentianInformationDate}" ui:field="lblCentianInfoDate"/>
                    </g:FlowPanel>
                    <g:FlowPanel>
                        <g:Label text="{msg.getMeterCentianKWhCreditRemaining}" ui:field="lblCentianKwhCreditRem"/>
                    </g:FlowPanel>
                    <g:FlowPanel>
                        <g:Label text="{msg.getMeterCentianCurrencyCreditRemaining}" ui:field="lblCentianCurrCredityRem"/>
                    </g:FlowPanel>
                    <g:FlowPanel>
                        <g:Label text="{msg.getMeterCentianNumDisconnections}" ui:field="lblCentianNumDisc"/>
                    </g:FlowPanel>
                    <g:FlowPanel >
                        <g:Label text="{msg.getMeterCentianTamperDetected}" ui:field="lblCentianTamperDetected" styleName="gwt-Label-iPayLink"/>
                        <g:Label ui:field="lblCentianTamperStates"/>
                    </g:FlowPanel>
             </ipay:FormGroupPanel>
            <ipay:FormGroupPanel ui:field="uriPanel" debugId="uriPanel" labelText="{msg.getMeterUriFields}" visible="false">
                  <ipay:FormRowPanel ui:field="uriPanelRow1">
                      <ipay:FormElement ui:field="meterUriAddressElement" debugId="meterUriAddressElement" helpMsg="{msg.getMeterUriAddressHelp}" labelText="{msg.getMeterUriAddress}:">
                          <g:TextBox debugId="txtbxMeterUriAddress" ui:field="txtbxMeterUriAddress" styleName="gwt-TextBox" visibleLength="31"/>
                      </ipay:FormElement>
                      <ipay:FormElement ui:field="meterUriPortElement" debugId="meterUriPortElement" helpMsg="{msg.getMeterUriPortHelp}" labelText="{msg.getMeterUriPort}:">
                          <ipay:BigDecimalValueBox debugId="txtbxMeterUriPort" ui:field="txtbxMeterUriPort" styleName="gwt-TextBox" visibleLength="7"/>
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
                  <ipay:FormRowPanel ui:field="uriPanelRow2">
                      <ipay:FormElement ui:field="meterUriProtocolElement" debugId="meterUriProtocolElement" helpMsg="{msg.getMeterUriProtocolHelp}" labelText="{msg.getMeterUriProtocol}:">
                          <g:TextBox debugId="txtbxMeterUriProtocol" ui:field="txtbxMeterUriProtocol" styleName="gwt-TextBox" visibleLength="18"/>
                      </ipay:FormElement>
                      <ipay:FormElement ui:field="meterUriParamsElement" debugId="meterUriParamsElement" helpMsg="{msg.getMeterUriParamsHelp}" labelText="{msg.getMeterUriParams}:">
                          <g:TextArea debugId="txtbxMeterUriParams" ui:field="txtbxMeterUriParams" styleName="gwt-TextBox" visibleLines="3"/>
                      </ipay:FormElement>
                  </ipay:FormRowPanel>
              </ipay:FormGroupPanel>
            <g:FlowPanel ui:field="selectStorePanel">
                <ipay:FormRowPanel>
                <g:Label styleName="gwt-Label-bold" ui:field="lblSelectMeterStore" />
                <ipay:FormElement ui:field="deviceStoreElement" helpMsg="{msg.getMeterSelectStoreHelp}" width="100%" required="true" labelText="{msg.getMeterSelectStoreAdd}: " >
                    <p3:IpayListBox debugId="meterStoreBox" visibleItemCount="1" ui:field="lstbxSelectStore" styleName="gwt-ListBox-ipay" multipleSelect="false"  />
                </ipay:FormElement>
                </ipay:FormRowPanel>
            </g:FlowPanel>
            <g:FlowPanel>
                <g:Label text="{msg.getMeterAssignedToUsagePoint}" ui:field="lblMeterAssigned" styleName="gwt-Label-bold-left"/>
            </g:FlowPanel>
            <g:FlowPanel>
                <g:Label ui:field="lblDateMeterInstalled" styleName="gwt-Label-bold-left" visible="false"/>
            </g:FlowPanel>
          </g:FlowPanel>
            <g:FlowPanel ui:field="requiredKeys">
                <g:Label horizontalAlignment="ALIGN_RIGHT" styleName="requiredLabel" text="{msg.getMeterRequiredText}" />
            </g:FlowPanel>
            <g:HTMLPanel ui:field="buttons" styleName="mainButtons">
              <g:Button debugId="meterSaveButton" text="{msg.getSaveButton}" ui:field="btnSave" />
              <g:Button debugId="meterCancelButton" text="{msg.getCancelButton}" ui:field="btnCancel" />
            </g:HTMLPanel>
         </g:FlowPanel>

         </g:FlowPanel>
    </g:DisclosurePanel>
    </g:HTMLPanel>
</ui:UiBinder> 
