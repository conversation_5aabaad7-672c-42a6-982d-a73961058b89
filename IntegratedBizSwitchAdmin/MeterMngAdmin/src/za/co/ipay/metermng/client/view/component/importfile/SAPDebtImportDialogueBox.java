package za.co.ipay.metermng.client.view.component.importfile;

import java.math.BigDecimal;
import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.sap.DebtImportRecord;

public class SAPDebtImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private DebtImportRecord recordIn;

    public SAPDebtImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        DebtImportRecord record = recordIn = itemDto.getSapDebtImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Supply Group", record.getSupplyGroup()));
        dataList.add(new ImportRecordField("Contract Account Number", record.getContractAccountNumber()));
        dataList.add(new ImportRecordField("Meter Number", record.getMeterNumber()));
        dataList.add(new ImportRecordField("PiggyBacking Percentage", record.getPiggyBackingPercentage().toPlainString()));
        dataList.add(new ImportRecordField("Arrears Total1", record.getArrearsTotal1().toPlainString()));
        dataList.add(new ImportRecordField("Arrears Total2", record.getArrearsTotal2().toPlainString()));
        dataList.add(new ImportRecordField("Refunds Total3", record.getRefundsTotal3().toPlainString()));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        DebtImportRecord chgRec = createRecordFromList();
        isDirtyData = false;
        if (!chgRec.getSupplyGroup().equals(recordIn.getSupplyGroup())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getContractAccountNumber().equals(recordIn.getContractAccountNumber())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getMeterNumber().equals(recordIn.getMeterNumber())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getPiggyBackingPercentage().equals(recordIn.getPiggyBackingPercentage())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getArrearsTotal1().equals(recordIn.getArrearsTotal1())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getArrearsTotal2().equals(recordIn.getArrearsTotal2())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getRefundsTotal3().equals(recordIn.getRefundsTotal3())) {
            isDirtyData = true;
            return;
        }
    }

    private DebtImportRecord createRecordFromList() {
        DebtImportRecord chgRec = new DebtImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Supply Group")) {
                chgRec.setSupplyGroup(field.getFieldValue());
            }
            if (field.getFieldname().equals("Contract Account Number")) {
                chgRec.setContractAccountNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("Meter Number")) {
                chgRec.setMeterNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("PiggyBacking Percentage")) {
                chgRec.setPiggyBackingPercentage(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("Arrears Total1")) {
                chgRec.setArrearsTotal1(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("Arrears Total2")) {
                chgRec.setArrearsTotal2(new BigDecimal(field.getFieldValue()));
            }
            if (field.getFieldname().equals("Refunds Total3")) {
                chgRec.setRefundsTotal3(new BigDecimal(field.getFieldValue()));
            }
        }
        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }

    @Override
    protected void displayUpdateMessage() {
        DebtImportRecord record = createRecordFromList();
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.item.update.success",
                        new String[] { record.getContractAccountNumber(), record.getMeterNumber() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItem) {
        importFileItem.setGenericImportRecord(createRecordFromList());
    }
}