package za.co.ipay.metermng.client.view.component;

import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

public class LabelledPanel extends Composite {
    private FlowPanel panel;
    private Label label;

    public LabelledPanel() {
        panel = new FlowPanel();
        label = new Label();
        panel.add(label);
        initWidget(panel);
    }
    
    public LabelledPanel(String labelText, Widget contents) {
        panel = new FlowPanel();
        this.label = new Label(labelText);
        panel.add(label);
        panel.add(contents);
        initWidget(panel);
    }
    
    public void setContents(Widget contents) {
        if(panel.getWidgetCount() > 1)
            panel.remove(1);
        panel.add(contents);
    }
    
    public void setLabelText(String labelText) {
        label.setText(labelText);
    }
    
}
