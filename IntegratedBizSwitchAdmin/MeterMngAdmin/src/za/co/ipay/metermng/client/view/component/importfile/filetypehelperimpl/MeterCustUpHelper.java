package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.logging.Logger;

import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.client.ui.Button;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.importfile.FileDetailPanel;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.workspace.bulkupload.metercustupbulkupload.ClientMeterCustUPBulkCsvMapToData;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.bulkupload.metercustup.CsvColumnData;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.group.GroupComponentType;
import za.co.ipay.metermng.shared.group.GroupTypeData;

public class MeterCustUpHelper extends BaseFiletypeHelper {

    private ArrayList<AppSetting> customFieldList;
    private String customFieldsStatusUnavailable;
    private ClientMeterCustUPBulkCsvMapToData clientMeterCustUPBulkCsvMapToData;
    private Map<String, List<String>> groupTypeNameSet = new LinkedHashMap<>();
    
    public MeterCustUpHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }
    
    @Override
    public void setRegReadingsMsgVisible(FileDetailPanel fileDetailPanel) {
        fileDetailPanel.setRegReadReminderVisibility(true);
    }
    
    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        JSONValue jsonValue = JSONParser.parseStrict(object.getImportFileRecordStr());
        JSONObject meterCustUpObject = jsonValue.isObject();
        if (meterCustUpObject.get("Meter Num") != null) {
            return meterCustUpObject.get("Meter Num").isString().stringValue();
        } else {
            return "";
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
    }

    @Override
    public void setExtractBtn(Button extractBtn, boolean isExtractAvailable) {
        extractBtn.setVisible(true);
        extractBtn.setText(MessagesUtil.getInstance().getMessage("button.import.extract.fail"));
    }
    
    @Override
    public void generateCsvDownload(final Logger logger, final ImportFileDto importFileDto) {
        //First constructAndInitDataMaps;
        clientFactory.getGroupRpc().getActiveGroupTypesWithHierarchy(new ClientCallback<ArrayList<GroupTypeData>>() {
            @Override
            public void onSuccess(ArrayList<GroupTypeData> result) {
                final List<GroupTypeData> fResult = result;
                clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {
                    @Override
                    public void onSuccess(ArrayList<AppSetting> result) {
                        customFieldList = result;
                        clientFactory.getUserInterfaceRpcAsync().getFormFields(new ClientCallback<Map<String, FormFields>>() {
                            @Override
                            public void onSuccess(Map<String, FormFields> formFields) {
                                initDataMap(fResult, formFields);
                                getDataAndDownload(logger, importFileDto);                            }
                        });
                    }
                });

            }
        });
    }

    private void initDataMap(List<GroupTypeData> groupTypeHierarchies, Map<String, FormFields> formFields) {
        customFieldsStatusUnavailable = MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase();
        clientMeterCustUPBulkCsvMapToData = new ClientMeterCustUPBulkCsvMapToData(customFieldList, customFieldsStatusUnavailable, groupTypeHierarchies, clientFactory.isEnableNonBillable(), formFields);
        initDataMap();

        // build groupTypeNameSet
        for (Entry<String, CsvColumnData> entry : clientMeterCustUPBulkCsvMapToData.getCsvHeadingToDataNameMap().entrySet()) {
            GroupComponentType groupComponentType = entry.getValue().getGroupComponentType();
            if (groupComponentType.equals(GroupComponentType.USAGE_POINT_GROUP)) {
                addToGroupTypeNameSet(entry, "group");

            } else if (groupComponentType.equals(GroupComponentType.LOCATION_GROUP)) {
                addToGroupTypeNameSet(entry, "main");
            }
        }
    }

    private void initDataMap() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.METERS_IN_STORE,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting result) {
                        if (result != null) {
                            boolean isMetersInStore = (Boolean.parseBoolean(result.getValue()));
                            clientMeterCustUPBulkCsvMapToData.initDataMap(isMetersInStore);
                        }
                    }
                });
    }

    private void addToGroupTypeNameSet(Entry<String, CsvColumnData> entry, String formName) {
        String groupTypeName = entry.getValue().getGroupTypeName();
        if (groupTypeNameSet.containsKey(groupTypeName)) {
            groupTypeNameSet.get(groupTypeName).add(entry.getKey());
        } else {
            List<String> groupHierarchies = new ArrayList<>();
            groupHierarchies.add(entry.getKey());
            groupTypeNameSet.put(groupTypeName, groupHierarchies);
        }
    }
    
    private void getDataAndDownload(final Logger logger, final ImportFileDto importFileDto) {
        parent.disableButtons();
        clientFactory.getImportFileDataRpc().extractFailedItems(importFileDto, new ClientCallback<List<ImportFileItem>>() {
            @Override
            public void onSuccess(List<ImportFileItem> itemList) {
                String csvContent = null; 
                if (itemList != null && !itemList.isEmpty()) {
                        csvContent = createDownloadStream(itemList);
                }        
                
                //in BaseHelper
                doDownload(logger, importFileDto, "_err", csvContent);
            }
            @Override
            public void onFailure(Throwable caught) {
                parent.enableButtons();
                super.onFailure(caught);
            }
        }); 
    }

    private String createDownloadStream(final List<ImportFileItem> itemList) {
        StringBuffer buffer = null;
        StringBuffer bufferLine2 = null;

        LinkedHashMap<String,CsvColumnData> dataMap = clientMeterCustUPBulkCsvMapToData.getCsvHeadingToDataNameMap();

        for (ImportFileItem item : itemList) {
            String data = item.getItemData();
            JSONValue jsonValue = JSONParser.parseStrict(data);
            JSONObject meterCustUpObject = jsonValue.isObject();
            Set<String> meterCustUpKeys = meterCustUpObject.keySet();


            //Generate headings on first line Json properties
            if (buffer == null) {
                buffer = new StringBuffer();
                bufferLine2 = new StringBuffer();
                for (String property : meterCustUpKeys) {
                    //generate Line 1 (required) & 2 (columnName) simultaneously
                    String groupTypeName = property;
                    if (groupTypeNameSet.containsKey(groupTypeName)) {
                        List<String> groupTypeHierarchyNames = groupTypeNameSet.get(groupTypeName);
                        for (String hierarchyName : groupTypeHierarchyNames) {
                            buffer.append(dataMap.get(hierarchyName).getInfoHeading()).append(",");
                            bufferLine2.append(hierarchyName).append(",");
                        }    

                    } else {
                        CsvColumnData colData = dataMap.get(property);
                        if (colData != null) {
                            buffer.append(colData.getInfoHeading()).append(",");
                        } else {
                            buffer.append("");
                        }
                        bufferLine2.append(property).append(",");
                    }
                }
                buffer.replace(buffer.length() - 1, buffer.length(), "\n");
                //if the first line doesn't contain an error for some reason (maybe did a selected import before?), add "Errors" heading now.
                if (!bufferLine2.toString().contains(MessagesUtil.getInstance().getMessage("bulk.upload.errors")))  {      //"Errors"
                    bufferLine2.append(MessagesUtil.getInstance().getMessage("bulk.upload.errors")).append(",");
                }
                bufferLine2.replace(bufferLine2.length() - 1, bufferLine2.length(), "\n");
                buffer.append(bufferLine2);
            }

            StringBuffer itemBuffer = new StringBuffer();
            for (String property : meterCustUpKeys) {
                itemBuffer.append(meterCustUpObject.get(property).isString().stringValue()).append(",");
            }
            if (item.getComment() != null && !item.getComment().isEmpty()) {
                itemBuffer.append(item.getComment()).append(",");
            }
            itemBuffer.replace(itemBuffer.length() - 1, itemBuffer.length(), "\n");
            buffer.append(itemBuffer);
        }

        return buffer.toString();
    }
}
