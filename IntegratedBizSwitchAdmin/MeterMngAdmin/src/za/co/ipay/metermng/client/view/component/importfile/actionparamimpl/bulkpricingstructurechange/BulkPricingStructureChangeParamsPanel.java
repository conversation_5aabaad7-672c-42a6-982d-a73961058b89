package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkpricingstructurechange;

import java.util.ArrayList;
import java.util.Date;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayDateBox;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkpricingstructurechange.BulkPricingStructureChangeParamRecord;

public class BulkPricingStructureChangeParamsPanel extends ActionParamsComponent
        implements ProvidesResize, RequiresResize {
    
    @UiField VerticalPanel bulkPricingStructureChangeParamsPanel;
    @UiField Label bulkPricingStructureChangeParamHeading;
    @UiField FormElement startDateElement;
    @UiField IpayDateBox startDateBox;
    @UiField FormElement pricingStructureElement;
    @UiField IpayListBox pricingStructureBox;

    private BulkPricingStructureChangeParamRecord bulkPricingStructureChangeParamRecord;
    private HasDirtyData hasDirtyData;
    HasDirtyDataManager hasDirtyDataManager;
    private static BulkPricingStructureChangeParamsPanelUiBinder uiBinder = GWT
            .create(BulkPricingStructureChangeParamsPanelUiBinder.class);

    interface BulkPricingStructureChangeParamsPanelUiBinder
            extends UiBinder<Widget, BulkPricingStructureChangeParamsPanel> {
    }

    public BulkPricingStructureChangeParamsPanel(ClientFactory clientFactory, String fileName,
            BulkPricingStructureChangeParamRecord bulkPricingStructureChangeParamRecord) {
        super();
        this.clientFactory = clientFactory;
        this.bulkPricingStructureChangeParamRecord = bulkPricingStructureChangeParamRecord;
        hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
        initWidget(uiBinder.createAndBindUi(this));
        bulkPricingStructureChangeParamHeading.setText(MessagesUtil.getInstance()
                .getMessage("bulk.pricing.structure.change.header", new String[] { fileName }));
        startDateBox.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
        addFieldHandlers();
        mapDataToForm();
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void addFieldHandlers() {
        startDateBox.addValueChangeHandler(new FormDataValueChangeHandler(hasDirtyData));
        startDateBox.addValueChangeHandler(new ValueChangeHandler<Date>() {
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                hasDirtyData.setDirtyData(true);
                startDateElement.clearErrorMsg();
                refreshPricingStructureBox(null);
            }
        });
        pricingStructureBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    private void refreshPricingStructureBox(String pricingStructureIdSaved) {
        final String pricingStructureId;
        if (pricingStructureIdSaved == null) {
            int pricingStructureIndex = pricingStructureBox.getSelectedIndex();
            if (pricingStructureIndex > 0) {
                pricingStructureId = pricingStructureBox.getItem(pricingStructureIndex).getValue();
            } else {
                pricingStructureId = null;
            }
        } else {
            pricingStructureId = pricingStructureIdSaved;
        }

        clientFactory.getLookupRpc().getPricingStructureLookupListForStartDate(startDateBox.getValue(),
                new ClientCallback<ArrayList<LookupListItem>>() {
                    @Override
                    public void onSuccess(ArrayList<LookupListItem> result) {
                        ArrayList<LookupListItem> removeItem = new ArrayList<LookupListItem>();
                        pricingStructureBox.clear();
                        if (clientFactory.isEnableAccessGroups() && clientFactory.isGroupGlobalUser()) {
                            //Global admins may only see global PS
                            for (LookupListItem ps : result) {
                                if (ps.getExtraInfoMap().get("accessGroupId") != null) {
                                    removeItem.add(ps);
                                }
                            }
                            for (LookupListItem item: removeItem) {
                                result.remove(item);
                            }
                        }
                        
                        result.add(0, new LookupListItem("-1", ""));
                        if (result != null) {
                            pricingStructureBox.setLookupItems(result);
                            if (pricingStructureId != null) {
                                pricingStructureBox.selectItemByValue(pricingStructureId);
                            }
                        }
                        pricingStructureBox.setEnabled(true);
                    }
                });
    }

    @Override
    public void mapDataToForm() {
        clearErrorMessages();
        if (bulkPricingStructureChangeParamRecord != null) {
            startDateBox.setValue(bulkPricingStructureChangeParamRecord.getStartDate());
            refreshPricingStructureBox(bulkPricingStructureChangeParamRecord.getPricingStructureId().toString());
        }
    }

    private void clearErrorMessages() {
        pricingStructureElement.clearErrorMsg();
        startDateElement.clearErrorMsg();
    }

    @Override
    public boolean validateForm() {
        clearErrorMessages();
        boolean isValidated = true;
        if (pricingStructureBox.getSelectedIndex() < 1) {
            Messages messages = MessagesUtil.getInstance();
            pricingStructureElement.setErrorMsg(messages.getMessage("error.field.is.required",
                    new String[] { messages.getMessage("bulk.pricing.structure.change.pricing.structure") }));
            isValidated = false;
        }
        Date startDate = startDateBox.getValue();
        if (startDate == null) {
            Messages messages = MessagesUtil.getInstance();
            startDateElement.setErrorMsg(messages.getMessage("error.field.is.required",
                    new String[] { messages.getMessage("bulk.pricing.structure.change.start.date") }));
            isValidated = false;
        } else if (new Date().after(startDate)) {
            Messages messages = MessagesUtil.getInstance();
            startDateElement.setErrorMsg(messages.getMessage("bulk.pricing.structure.change.ps.start.date.error",
                    new String[] { messages.getMessage("bulk.pricing.structure.change.start.date") }));
            isValidated = false;
        }
        return isValidated;
    }

    @Override
    public BulkParamRecord mapFormToData() {
        bulkPricingStructureChangeParamRecord = new BulkPricingStructureChangeParamRecord();
        bulkPricingStructureChangeParamRecord.setStartDate(startDateBox.getValue());
        bulkPricingStructureChangeParamRecord.setPricingStructureId(
                Long.valueOf(pricingStructureBox.getItem(pricingStructureBox.getSelectedIndex()).getValue()));
        return bulkPricingStructureChangeParamRecord;
    }

    @Override
    public boolean checkDirtyData() {
        return hasDirtyData.isDirtyData();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                bulkPricingStructureChangeParamsPanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
