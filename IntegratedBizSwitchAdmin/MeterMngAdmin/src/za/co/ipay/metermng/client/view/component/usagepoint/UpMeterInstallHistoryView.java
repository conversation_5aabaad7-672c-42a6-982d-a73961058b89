package za.co.ipay.metermng.client.view.component.usagepoint;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;

public class UpMeterInstallHistoryView extends BaseComponent implements IpayDataProviderFilter<UpMeterInstallHistData> {

    private enum ColumnsE {
        DATE_MODIFIED("usagepoint.hist.datemod"), USER("usagepoint.hist.user"), ACTION("usagepoint.hist.action"),
        USAGE_POINT("usagepoint.title"), METER("usagepoint.txn.meter"), INSTALL_DATE("bulk.upload.installationdate"),
        REMOVE_DATE("up_meter_install.remove.date"), LATITUDE("metadata.lat.label"), LONGITUDE("metadata.lon.label"),
        INSTALL_REF("up_meter_install.install.ref"), REMOVE_REF("up_meter_install.remove.ref");

        String key;

        private ColumnsE(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    };

    private static final int DEFAULT_PAGE_SIZE = 15;

    interface UpMeterInstallHistoryWidgetUiBinder extends UiBinder<Widget, UpMeterInstallHistoryView> {
    }

    private static UpMeterInstallHistoryWidgetUiBinder uiBinder = GWT.create(UpMeterInstallHistoryWidgetUiBinder.class);

    @UiField(provided=true) CellTable<UpMeterInstallHistData> clltblHistory;
    @UiField TablePager smplpgrHistory;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    
    private IPayDataProvider<UpMeterInstallHistData> dataProvider;
    private ArrayList<UpMeterInstallHistData> thehistorydata;
    private ListHandler<UpMeterInstallHistData> columnSortHandler;
    private boolean viewConstructed = false;
    private boolean forMeter;

    public UpMeterInstallHistoryView(ClientFactory clientFactory, boolean forMeter) {
        this.clientFactory = clientFactory;
        this.forMeter = forMeter;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initView();
        initTable(false);

        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        Messages messages = MessagesUtil.getInstance();
        filterDropdown.addItem(messages.getMessage(ColumnsE.USER.getKey()));
        filterDropdown.addItem(messages.getMessage(ColumnsE.DATE_MODIFIED.getKey()));
        if (forMeter) {
            filterDropdown.addItem(messages.getMessage(ColumnsE.USAGE_POINT.getKey()));
        } else {
            filterDropdown.addItem(messages.getMessage(ColumnsE.METER.getKey()));
        }
    }

    void initTable(boolean refresh) {
        if (dataProvider == null && refresh == false) {
            dataProvider = new IPayDataProvider<UpMeterInstallHistData>(this);

            columnSortHandler = new ListHandler<UpMeterInstallHistData>(dataProvider.getList());
            String headings[] = getHeadings();
            int index = 0;
            for (ColumnsE field : ColumnsE.values()) {
                if ((forMeter && field != ColumnsE.METER) || (!forMeter && field != ColumnsE.USAGE_POINT)) {
                    clltblHistory.addColumn(createColumn(columnSortHandler, field), headings[index]);
                }
                index++;
            }

            clltblHistory.addColumnSortHandler(columnSortHandler);
            dataProvider.addDataDisplay(clltblHistory);
            smplpgrHistory.setDisplay(clltblHistory);
        } else {
            clltblHistory.redraw();
        }
        dateFilter.setDataProvider(dataProvider);
    }

    private void createTable() {
        clltblHistory = new CellTable<UpMeterInstallHistData>(DEFAULT_PAGE_SIZE,
                ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private String[] getHeadings() {
        Messages messagesInstance = MessagesUtil.getInstance();
        String headings[] = new String[ColumnsE.values().length];
        int i = 0;
        for (ColumnsE field : ColumnsE.values()) {
            headings[i++] = messagesInstance.getMessage(field.getKey());
        }
        return headings;
    }

    private Column<UpMeterInstallHistData, ?> createColumn(ListHandler<UpMeterInstallHistData> columnSortHandler,
            final ColumnsE columnType) {
        TextColumn<UpMeterInstallHistData> column = new TextColumn<UpMeterInstallHistData>() {
            @Override
            public String getValue(UpMeterInstallHistData data) {
                Object value = getDataValue(columnType, data);
                if (value instanceof Date) {
                    return FormatUtil.getInstance().formatDateTime((Date) value);
                } else {
                    return getObjectAsText(value);
                }
            }
        };
        columnSortHandler.setComparator(column, new Comparator<UpMeterInstallHistData>() {
            public int compare(UpMeterInstallHistData o1, UpMeterInstallHistData o2) {
                Object value1 = getDataValue(columnType, o1);
                Object value2 = getDataValue(columnType, o2);
                if (value1 == null && value2 == null) {
                    return 0;
                } else if (value1 instanceof Date || value2 instanceof Date) {
                    if (value1 == null) {
                        return 1;
                    } else if (value2 == null) {
                        return -1;
                    }
                    return ((Date) value1).compareTo((Date) value2);
                } else {
                    return value1.toString().compareTo(value2.toString());
                }
            }
        });
        column.setSortable(true);
        return column;
    }

    private Object getDataValue(ColumnsE columnType, UpMeterInstallHistData data) {
        switch (columnType) {
        case INSTALL_DATE:
            return data.getInstallDate();
        case INSTALL_REF:
            return getObjectAsText(data.getInstallRef());
        case LATITUDE:
            return getObjectAsText(data.getLatitude());
        case LONGITUDE:
            return getObjectAsText(data.getLongitude());
        case METER:
            return data.getMeterNumber();
        case REMOVE_DATE:
            return data.getRemoveDate();
        case REMOVE_REF:
            return getObjectAsText(data.getRemoveRef());
        case USAGE_POINT:
            return data.getUsagePointName();
        case ACTION:
            return data.getUserAction();
        case DATE_MODIFIED:
            return data.getDateRecModified();
        case USER:
            return data.getUserRecEntered();
        }
        return null;
    }

    private String getObjectAsText(Object value) {
        if (value == null) {
            return "";
        }
        return String.valueOf(value);
    }

    public void setUpMeterInstallHistDataList(ArrayList<UpMeterInstallHistData> thedata) {
        thehistorydata = thedata;
        dataProvider.setList(thehistorydata);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<UpMeterInstallHistData>(dataProvider.getList());
        } else {
            columnSortHandler.setList(dataProvider.getList());
        }
        ColumnSortEvent.fire(clltblHistory, clltblHistory.getColumnSortList());
    }

    @Override
    public boolean isValid(UpMeterInstallHistData value, String filter) {
        String filterOption = filterDropdown.getSelectedItemText();
        Messages messages = MessagesUtil.getInstance();
        if (filterOption.equals(messages.getMessage(ColumnsE.DATE_MODIFIED.getKey()))) {
            return dateFilter.isValid(value.getDateRecModified(), filter);
        } else {
            String filterValue = value.getUserRecEntered();
            if (filterOption.equals(messages.getMessage(ColumnsE.METER.getKey()))) {
                filterValue = value.getMeterNumber();
            } else if (filterOption.equals(messages.getMessage(ColumnsE.USAGE_POINT.getKey()))) {
                filterValue = value.getUsagePointName();
            }
            return filterValue.toLowerCase().contains(filter.toLowerCase());
        }
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(thehistorydata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrHistory.firstPage();
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage(ColumnsE.DATE_MODIFIED.getKey()));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }
}
