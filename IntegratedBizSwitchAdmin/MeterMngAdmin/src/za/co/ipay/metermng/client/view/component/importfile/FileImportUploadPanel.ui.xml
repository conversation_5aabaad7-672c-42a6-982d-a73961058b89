<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
    xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client"
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:form="urn:import:za.co.ipay.gwt.common.client.form">
    
    <ui:style>
        .cellTable {
          border-bottom: 1px solid #ccc;
          text-align: left;
          margin-bottom: 4px;
        }
        .ctufileupload {
          margin-left: 5px;
          margin-top:5px;
        }
        .ctuMarginAbove {
            margin-top:10px;
        }
        .ctuLabelBold{
            font-weight: bold;
        }
        .addSpacing {
          margin-right: 5px;
       }            
    </ui:style>
  
   <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
   
                <g:FlowPanel>
                  <g:FlowPanel styleName="formElementsPanel">
                    <g:DisclosurePanel ui:field="fileUploadDisclosurePanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" open="true">
                        <g:header>
                            <ui:text from="{msg.getFileUploadTitle}" />
                        </g:header>
                      <g:HTMLPanel>
                        <g:VerticalPanel spacing="10" styleName="formElementsPanel" ui:field="uploadFilePanel">
                        
                            <g:HTML ui:field="uploadFileDescription" styleName="dataDescription" />
                            <g:FormPanel ui:field="uploadFileFormPanel">
                                <g:VerticalPanel>
                                    <form:FormRowPanel>
                                        <form:FormElement ui:field="fileTypeElement" helpMsg="{msg.getFileTypeSelectHelp}" labelText="{msg.getFileTypeLabelText}:" required="true" debugId="fileTypeElement" >
                                            <p2:IpayListBox visibleItemCount="1" ui:field="lstbxFileType" styleName="gwt-ListBox-ipay" multipleSelect="false" debugId="fileType"></p2:IpayListBox>
                                        </form:FormElement>
                                    </form:FormRowPanel>
                                    
                                    <g:Hidden name="fileTypeName" value="" ui:field="fileTypeName" />
                                    <g:Hidden name="enableAccessGroups" value="" ui:field="enableAccessGroups" />
                                    <form:FormRowPanel>
                                        <form:FormElement ui:field="uploadFileElement" helpMsg="{msg.getUploadFileSelectHelp}" labelText="{msg.getUploadFileLabelText}:" debugId="uploadFileElement" >
                                            <g:FileUpload ui:field="uploadFile" name="file" title="uploadFile" styleName="{style.ctufileupload}" width="500px" debugId="uploadFile"></g:FileUpload>
                                        </form:FormElement>
                                    </form:FormRowPanel>
                                </g:VerticalPanel>
                            </g:FormPanel>            

                            <g:FlowPanel>
                                <g:Button ui:field="btnDownloadTemplate" text="{msg.getDownloadDefaultTempleteText}" visible="false" styleName="gwt-Button {style.addSpacing}"/>
                                <g:Button ui:field="btnUploadFile" debugId="btnUploadFile" text="{msg.getUploadFileButton}"/>
                            </g:FlowPanel>
 
                        </g:VerticalPanel>
                      </g:HTMLPanel>
                    </g:DisclosurePanel>
                  </g:FlowPanel>
               </g:FlowPanel>
    
</ui:UiBinder> 