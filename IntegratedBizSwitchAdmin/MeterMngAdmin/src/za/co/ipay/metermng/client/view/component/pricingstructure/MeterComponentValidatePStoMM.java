package za.co.ipay.metermng.client.view.component.pricingstructure;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.MeterComponent;
import za.co.ipay.metermng.client.view.component.UsagePointComponent;
import za.co.ipay.metermng.shared.dto.PSDto;

public class MeterComponentValidatePStoMM extends AbstractValidatePStoMM {
    
    private MeterComponent parent;

    public MeterComponentValidatePStoMM(MeterComponent parent) {
        super();
        this.parent = parent;
    }

    public void isregReadPsSameBillingDetsAsMeterModel(ClientFactory clientFactory, List<PSDto> pricingStructureDtos,
            Long mdcId, String userName, Logger logger) {
        super.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pricingStructureDtos, mdcId, userName, logger, "MeterComponent");
    }
    
    @Override
    public void errorNoTariff() {
        parent.errorOrNonMatchPsToMm();
    }

    @Override
    public void noneMatchContinue() {
        parent.errorOrNonMatchPsToMm();
    }

    @Override
    public void partialMatchConfirmContinue() {
        parent.passToSaveContinue();
    }

    @Override
    public void partialMatchDenyContinue() {
        parent.partialMatchDenyContinue();
    }

    @Override
    public void exactMatchOrNoDataContinue() {
        parent.passToSaveContinue();
    }

}
