package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Image;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.dto.importfile.KeyChangeDto;

public class BulkKeyChangeHelper extends BaseFiletypeHelper {

    public BulkKeyChangeHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }
    
    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getBulkKeyChangeImportRecord().getMeterNum();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
    }
    
    @Override
    public String getWaitingText() {
        return messagesInstance.getMessage("import.upload.twirly.waiting.text.keychange");   
    }

    @Override
    public void setExtractBtn(Button extractBtn, boolean isExtractAvailable) {
        if (isExtractAvailable) {
            extractBtn.setVisible(true);
            extractBtn.setText(MessagesUtil.getInstance().getMessage("button.import.extract.keychanges"));
        } else {
            extractBtn.setVisible(false);
        }
    }
    
    @Override
    public void setSpareBtn(Button spareBtn) {
        spareBtn.setVisible(true);
        spareBtn.setText(MessagesUtil.getInstance().getMessage("button.import.extract.fail"));
    }
    
    @Override
    public void handleSpareBtn(final Logger logger, final ImportFileDto importFileDto) {
        //for BulkKeyChange the SpareBtn does an extract of failed keychanges
        parent.disableButtons();
        clientFactory.getImportFileDataRpc().extractFailedItems(importFileDto, new ClientCallback<List<ImportFileItem>>() {
            @Override
            public void onSuccess(List<ImportFileItem> itemList) {
                String csvContent = null; 
                if (itemList != null && !itemList.isEmpty()) {
                        csvContent = createDownloadStreamFailedItems(itemList);
                }        
                
                //in BaseHelper
                doDownload(logger, importFileDto, "_err", csvContent);
            }
            @Override
            public void onFailure(Throwable caught) {
                parent.enableButtons();
                super.onFailure(caught);
            }
        }); 
        
    }
    
    private String createDownloadStreamFailedItems(final List<ImportFileItem> itemList) {
        StringBuffer buffer = new StringBuffer();
        //Add Column Heading
        buffer.append(MessagesUtil.getInstance().getMessage("meter.number")).append("\n");
        
        for (ImportFileItem item : itemList) {
            String data = item.getItemData();
            JSONValue jsonValue = JSONParser.parseStrict(data);
            JSONObject meterCustUpObject = jsonValue.isObject();
            buffer.append(meterCustUpObject.get("meterNum").isString().stringValue()).append("\n");
        }
        return buffer.toString();
    }
    
    @Override
    public void generateCsvDownload(final Logger logger, final ImportFileDto importFileDto) {
        String bulkRef = importFileDto.getBulkRef();
        if (bulkRef == null || bulkRef.isEmpty()) {
            Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("bulk.Keychange.extract.none"), 
                    new Image(MediaResourceUtil.getInstance().getInformationIcon()), 
                    StyleNames.POPUP_MESSAGE, 
                    MessagesUtil.getInstance().getMessage("button.close"), null,
                    false, true);
        } else {
            clientFactory.getImportFileDataRpc().getKeyChangeTokensforBulkRef(bulkRef, new ClientCallback<List<KeyChangeDto>>() {
                @Override
                public void onSuccess(List<KeyChangeDto> itemList) {
                    String csvContent = null; 
                    if (itemList != null && !itemList.isEmpty()) {
                        csvContent = createDownloadStream(itemList, importFileDto.getImportFilename());
                    } else {
                        Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("bulk.Keychange.extract.none"), 
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        return;
                    }

                    //in BaseHelper
                    doDownload(logger, importFileDto, null, csvContent);
                }
                @Override
                public void onFailure(Throwable caught) {
                    parent.enableButtons();
                    super.onFailure(caught);
                }
            }); 
        }
    }
    
    private String createDownloadStream(final List<KeyChangeDto> tokenList, String importFileName) {
        DateTimeFormat dateTimeFormat = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");
        StringBuffer buffer = new StringBuffer();

        Messages msg = MessagesUtil.getInstance();
        
        //Add Column Headings
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.meterNum")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.userRef")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.token1")).append(",");
        
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.token2")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.token3")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.token4")).append(",");
        
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.fromSupGroup")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.fromKeyRev")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.fromTariffIdx")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.fromBaseDate")).append(",");
        
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.toSupGroup")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.toKeyRev")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.toTariffIdx")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.toBaseDate")).append(",");
        
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.transDate")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.userRecEntered")).append(",");
        buffer.append(msg.getMessage("bulk.Keychange.extract.label.importFileName")).append("\n");
        
        //token lines
        for (KeyChangeDto t : tokenList) {
            buffer.append(t.getMeterNum()).append(",");
            buffer.append(t.getUserRef() == null ? "" : t.getUserRef()).append(",");
            
            buffer.append(getFormattedToken(t.getToken1())).append(",");
            buffer.append(getFormattedToken(t.getToken2())).append(",");
            buffer.append(getFormattedToken(t.getToken3())).append(",");
            buffer.append(getFormattedToken(t.getToken4())).append(",");
            
            buffer.append(t.getFromSupGroup()).append(",");
            buffer.append(t.getFromKeyRev()).append(",");
            buffer.append(t.getFromTariffIdx()).append(",");
            buffer.append(makeEmptyIfNull(t.getFromBaseDate())).append(",");
            
            buffer.append(t.getToSupGroup()).append(",");
            buffer.append(t.getToKeyRev()).append(",");
            buffer.append(t.getToTariffIdx()).append(",");
            buffer.append(makeEmptyIfNull(t.getToBaseDate())).append(",");
            
            buffer.append(dateTimeFormat.format(t.getTransDate())).append(",");
            buffer.append(t.getUserRecEntered()).append(",");
            buffer.append(importFileName).append("\n");
        }
        
        return buffer.toString();
    }
    
    private String makeEmptyIfNull(Short baseDt) {
        if (baseDt == null) {
            return "";
        } else {
            return baseDt.toString();
        }
    }

    private String getFormattedToken(String str) {
        String key = (str == null) ? "" : str;
        int keyLength = key.length();
        if (keyLength == 0) {
            //to save processing time
            return key;
        }
        
        int a = keyLength / 4;
        String token = "";
        for (int i = 0; i < a; i++) {
            token += key.substring(i * 4, (i + 1) * 4).concat(" ");
        }
        return token += key.substring(a * 4, keyLength);
    }
}
