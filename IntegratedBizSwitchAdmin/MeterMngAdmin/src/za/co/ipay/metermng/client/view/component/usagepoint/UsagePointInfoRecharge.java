package za.co.ipay.metermng.client.view.component.usagepoint;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.maps.client.base.LatLng;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SimplePanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.MapBaseComponent;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;

public class UsagePointInfoRecharge extends MapBaseComponent {

	private static UsagePointInfoRechargeUiBinder uiBinder = GWT.create(UsagePointInfoRechargeUiBinder.class);
	   
	interface UsagePointInfoRechargeUiBinder extends UiBinder<Widget, UsagePointInfoRecharge> {
    }
   
    @UiField UsagePointRechargeChart chrtUpRecharge;

    @UiField VerticalPanel closeRetailersPanel;
    @UiField SimplePanel mapPanel;

    private Double longitude, latitude;
    private UsagePointData usagePointData;
    private List<GisMetadata> gisMetadata;
    private boolean viewConstructed = false;

    public UsagePointInfoRecharge(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        mapPanel.clear();
        Label addcoordinates = new Label(MessagesUtil.getInstance().getMessage("meter.coords"));
        addcoordinates.setStylePrimaryName("gwt-Label");
        mapPanel.add(addcoordinates);

        setDemoMode(clientFactory.isDemoMode());
        chrtUpRecharge.setVisible(false);
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    public void setDemoMode(boolean demoMode) {
        closeRetailersPanel.setVisible(demoMode);
    }

    public void setUsagePointInfo(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
        chrtUpRecharge.setUsagePointId(usagePointData.getId());
        chrtUpRecharge.setVisible(usagePointData.getId() != null);
    }

    public void setMapCoordinates(LocationData locationData) {
        if (locationData != null) {
            this.latitude = locationData.getLatitude();
            this.longitude = locationData.getLongitude();
        }
    }

    public void loadMapPanel() {
        LatLng mapCoordinates = null;
        if (latitude != null && longitude != null) {
            mapCoordinates = LatLng.newInstance(latitude, longitude);
        }
        List<GisMetadata> gisMetadata = getGisMetadata();
        String defaultLabel = MessagesUtil.getInstance().getMessage("usagepoint.title") + ": " + usagePointData.getName();
        loadMapPanel(mapPanel, mapCoordinates, gisMetadata, defaultLabel);
    }

    public List<GisMetadata> getGisMetadata() {
        return gisMetadata != null ? gisMetadata : new ArrayList<GisMetadata>(0);
    }

    public void setGisMetadata(List<GisMetadata> gisMetadata) {
        this.gisMetadata = gisMetadata;
    }
}
