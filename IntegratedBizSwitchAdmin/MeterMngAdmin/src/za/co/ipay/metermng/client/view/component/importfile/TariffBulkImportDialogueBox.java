package za.co.ipay.metermng.client.view.component.importfile;

import java.util.List;

import com.google.gwt.json.client.JSONObject;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.tariffexport.TariffPsImportRecord;

public class TariffBulkImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private TariffPsImportRecord recordIn;

    public TariffBulkImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        TariffPsImportRecord record = recordIn = itemDto.getTariffPsImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("pricingStructureNameDONOTCHANGE", record.getPricingStructureNameDONOTCHANGE()));
        dataList.add(new ImportRecordField("calcClassDONOTCHANGE", record.getCalcClassDONOTCHANGE()));
        dataList.add(new ImportRecordField("psRecordStatus", record.getPsRecordStatus().toString()));
        dataList.add(new ImportRecordField("tariffName", record.getTariffName()));
        dataList.add(new ImportRecordField("startDate", record.getStartDate()));
        dataList.add(new ImportRecordField("description", record.getDescription()));
        dataList.add(new ImportRecordField("customerDescription", record.getCustomerDescription()));
        dataList.add(new ImportRecordField("calcContents", record.getCalcContents(), JSONObject.class.getSimpleName()));
        return dataList;
    }
    
    @Override
    protected void checkDirtyData() {
        
        TariffPsImportRecord chgRec = createRecordFromList();;
        
        if (!recordIn.getPricingStructureNameDONOTCHANGE().equals(chgRec.getPricingStructureNameDONOTCHANGE())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getCalcClassDONOTCHANGE().equals(chgRec.getCalcClassDONOTCHANGE())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getPsRecordStatus().equals(chgRec.getPsRecordStatus())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getTariffName().equals(chgRec.getTariffName())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getStartDate().equals(chgRec.getStartDate())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getDescription().equals(chgRec.getDescription())) {
            isDirtyData = true;
            return;
        };
        if (!recordIn.getCustomerDescription().equals(chgRec.getCustomerDescription())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getCalcContents().equals(chgRec.getCalcContents())) {
            isDirtyData = true;
            return;
        }
    }

    private TariffPsImportRecord createRecordFromList() {
        
        TariffPsImportRecord chgRec = new TariffPsImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("pricingStructureNameDONOTCHANGE")) {
                chgRec.setPricingStructureNameDONOTCHANGE(field.getFieldValue());
            }
            if (field.getFieldname().equals("calcClassDONOTCHANGE")) {
                chgRec.setCalcClassDONOTCHANGE(field.getFieldValue());
            }
            if (field.getFieldname().equals("psRecordStatus")) {
                if (field.getFieldValue().equals("ACT")) {
                    chgRec.setPsRecordStatus(RecordStatus.ACT);
                } else {
                    chgRec.setPsRecordStatus(RecordStatus.DAC);
                }
            }
            if (field.getFieldname().equals("tariffName")) {
                chgRec.setTariffName(field.getFieldValue());
            }
            if (field.getFieldname().equals("startDate")) {
                chgRec.setStartDate(field.getFieldValue());
            }
            if (field.getFieldname().equals("description")) {
                chgRec.setDescription(field.getFieldValue());
            }
            if (field.getFieldname().equals("customerDescription")) {
                chgRec.setCustomerDescription(field.getFieldValue());
            }
            if (field.getFieldname().equals("calcContents")) {
                chgRec.setCalcContents(field.getFieldValue());
            }
        }
        
        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }

    @Override
    protected void displayUpdateMessage() {
        TariffPsImportRecord result = createRecordFromList();
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.item.update.bulk.tariff.success",
                        new String[] { result.getTariffName() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }
}