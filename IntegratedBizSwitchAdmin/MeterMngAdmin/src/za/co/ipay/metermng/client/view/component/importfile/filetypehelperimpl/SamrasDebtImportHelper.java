package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.SamrasDebtImportDialogueBox;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class SamrasDebtImportHelper extends BaseFiletypeHelper {

    public SamrasDebtImportHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }

    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new SamrasDebtImportDialogueBox(clientFactory, parent);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getSamrasDebtImportRecord().getAccountNumber();
    }

    @Override
    public String getUsagePointColumnValue(ImportFileItemDto object) {
        return object.getSamrasDebtImportRecord().getArrearsBalance().toPlainString();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.account.number"));
        table.addColumn(tableColumnMap.get("usagePointCol"), messagesInstance.getMessage("import.arrears.balance"));
    }
}
