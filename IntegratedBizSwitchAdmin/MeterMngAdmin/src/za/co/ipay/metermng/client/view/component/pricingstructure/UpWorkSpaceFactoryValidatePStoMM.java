package za.co.ipay.metermng.client.view.component.pricingstructure;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.UsagePointComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceFactory;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.PSDto;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class UpWorkSpaceFactoryValidatePStoMM extends AbstractValidatePStoMM {
    
    private UsagePointWorkspaceFactory parent;
    private UsagePointData usagePointData;
    private LocationData serviceLocation;
    private LocationData usagePointLocation;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private String[] confirmFeedbackMessages;
    private String[] cancelFeedbackMessages;

    public UpWorkSpaceFactoryValidatePStoMM(UsagePointWorkspaceFactory parent) {
        super();
        this.parent = parent;
    }

    public void isregReadPsSameBillingDetsAsMeterModel(ClientFactory clientFactory, List<PSDto> pricingStructureDtos,
            Long mdcId, String userName, Logger logger, 
            UsagePointData usagePointData, LocationData serviceLocation,
            LocationData usagePointLocation, UsagePointWorkspaceView usagePointWorkspaceView,
            String[] confirmFeedbackMessages, String[] cancelFeedbackMessages) {
        this.usagePointData = usagePointData;
        this.serviceLocation = serviceLocation;
        this.usagePointLocation = usagePointLocation;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.confirmFeedbackMessages = confirmFeedbackMessages;
        this.cancelFeedbackMessages = cancelFeedbackMessages;

        super.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pricingStructureDtos, mdcId, userName, logger, "UsagePointWorkspaceFactory");
    }
    
    @Override
    public void errorNoTariff() {
    }

    @Override
    public void noneMatchContinue() {
    }

    @Override
    public void partialMatchConfirmContinue() {
        parent.queryActivateAndSaveUsagePointCheckChannels(usagePointData, serviceLocation,
                usagePointLocation, usagePointWorkspaceView,
                confirmFeedbackMessages, cancelFeedbackMessages);
    }

    @Override
    public void partialMatchDenyContinue() {
    }

    @Override
    public void exactMatchOrNoDataContinue() {
        parent.queryActivateAndSaveUsagePointCheckChannels(usagePointData, serviceLocation,
                usagePointLocation, usagePointWorkspaceView,
                confirmFeedbackMessages, cancelFeedbackMessages);
    }

}
