package za.co.ipay.metermng.client.view.component;


import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;

public class MridComponent extends BaseComponent {

    private static MridComponentUiBinder uiBinder = GWT.create(MridComponentUiBinder.class);

    @UiField TextBox txtbxMrid;
    @UiField FormElement txtbxMridElement;
    
    @UiField CheckBox chckbxMridExternal;
    @UiField FormElement mridExternalElement;

	private HasDirtyData hasDirtyData;
    interface MridComponentUiBinder extends UiBinder<Widget, MridComponent> {}

	/**
	 * hasDirtyData is optional, should be supplied with forms that have save/cancel approach in non-modal forms
	 */
    public MridComponent() {
        this(null, null, null, false);
    }
    
    public MridComponent(String textBoxValue, Boolean isExternal) {
        this(null, null, textBoxValue, isExternal);
    }
    
	public MridComponent(ClientFactory clientFactory, HasDirtyData hasDirtyData, String textBoxValue, Boolean isExternal) {
	    this.hasDirtyData = hasDirtyData;
	    this.clientFactory = clientFactory;
	    initWidget(uiBinder.createAndBindUi(this));
	    addFieldHandlers();
		populateFields(textBoxValue, isExternal);
    }

    public void populateFields(String mrid, boolean isExternal) {
        txtbxMrid.setText(mrid);
        txtbxMrid.setEnabled(isExternal);
        chckbxMridExternal.setChecked(isExternal);
    }

    public boolean validate() {
        if (txtbxMrid.getValue().trim().isEmpty()) {
            txtbxMridElement.setErrorMsg(MessagesUtil.getInstance().getMessage("mrid.component.error"));
            return false;
        }
        return true;
    }

    public void clearErrorMsg() {
        txtbxMridElement.clearErrorMsg();
    }

    private void addFieldHandlers() {
        if(hasDirtyData != null) {
            txtbxMrid.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
            chckbxMridExternal.addClickHandler(new FormDataClickHandler(hasDirtyData));
        }
    }
    
    
    public void addFieldHandlers(SimpleForm form) {
        if(form != null) {
            txtbxMrid.addChangeHandler(new FormDataChangeHandler(form));
            chckbxMridExternal.addClickHandler(new FormDataClickHandler(form));
        }
    }
    
    public void setDirtyData(HasDirtyData d) {
        this.hasDirtyData = d;
    }
    
    public void initMrid(ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        clientFactory.getMeterRpc().getNewMrid(new ClientCallback<String>() {
            @Override
            public void onSuccess(String result) {
                if (txtbxMrid.getText() == null || txtbxMrid.getText().trim().isEmpty()) {
                    setMrid(result);     
                }
            }
        });
        setIsExternal(false);
        setMridEnabled(false);
    }

    @UiHandler("chckbxMridExternal")
    void handleMridCheck(ClickEvent event) {
        txtbxMrid.setEnabled(chckbxMridExternal.getValue());
        if (!chckbxMridExternal.getValue() && clientFactory != null) {
            if (txtbxMrid.getText() == null || txtbxMrid.getText().trim().isEmpty()) {
                initMrid(clientFactory);
            }   
        }
    }

    public TextBox getTxtbxMrid() {
        return txtbxMrid;
    }

    public FormElement getTxtbxMridElement() {
        return txtbxMridElement;
    }

    public CheckBox getChckbxMridExternal() {
        return chckbxMridExternal;
    }

    public FormElement getMridExternalElement() {
        return mridExternalElement;
    }
    
    public String getMrid() {
        return txtbxMrid.getValue();
    }
    
    public boolean isExternal() {
        return chckbxMridExternal.getValue();
    }
    
    public void setMrid(String mrid) {
        txtbxMrid.setValue(mrid);
    }
    
    public void setIsExternal(boolean isExternal) {
        chckbxMridExternal.setValue(isExternal);
        txtbxMrid.setEnabled(isExternal);
    }
    
    public void setMridEnabled(boolean enabled) {
        txtbxMrid.setEnabled(enabled);
    }
}
