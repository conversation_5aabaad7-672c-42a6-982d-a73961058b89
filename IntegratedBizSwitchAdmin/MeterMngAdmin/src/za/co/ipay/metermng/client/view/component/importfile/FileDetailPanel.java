package za.co.ipay.metermng.client.view.component.importfile;

import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.importfile.customwidgets.LabelGroupElement;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;

public class FileDetailPanel extends BaseComponent {
    
    @UiField HTML dataName;
    @UiField LabelGroupElement fileNameGrpEl;
    @UiField LabelGroupElement fileTypeNameGrpEl;
    @UiField LabelGroupElement numItemsGrpEl;
    @UiField LabelGroupElement upLoadStartDateGrpEl;
    @UiField LabelGroupElement upLoadEndDateGrpEl;
    @UiField LabelGroupElement uploadUserNameGrpEl;
    @UiField LabelGroupElement uploadnumFailedGrpEl;
    @UiField LabelGroupElement lastImportStartDateGrpEl;
    @UiField LabelGroupElement lastImportEndDateGrpEl;
    @UiField LabelGroupElement lastImportUserNameGrpEl;
    
    @UiField FormRowPanel bulkRefRowPanel;
    @UiField LabelGroupElement bulkRefGrpEl;
    @UiField HTML regReadReminder;
    
    @UiField HTML stillBusyText;
    @UiField HTML warnMaxLicenseExceeded;
    
    private ImportFileItemView parentView;
    
    private static FileDetailPanelUiBinder uiBinder = GWT.create(FileDetailPanelUiBinder.class);

    interface FileDetailPanelUiBinder extends UiBinder<Widget, FileDetailPanel> {
    }

    public FileDetailPanel() {
        initWidget(uiBinder.createAndBindUi(this));
        warnMaxLicenseExceeded.setVisible(false);
        dataName.setText(MessagesUtil.getInstance().getMessage("import.items.file.detail.header"));
    }
    
    public FileDetailPanel(ImportFileItemView parentView) {
        this();
        this.parentView = parentView;
    }
    
    public void setLabels(ImportFileDto importFileDto) {
        fileNameGrpEl.setDataLabel(importFileDto.getImportFilename());
        fileTypeNameGrpEl.setDataLabel(importFileDto.getImportFileTypeName());
        numItemsGrpEl.setDataLabel(String.valueOf(importFileDto.getNumItems()));
        
        if (importFileDto.getUploadStart() != null) {
            upLoadStartDateGrpEl.setDataLabel(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()).format(importFileDto.getUploadStart()));
        }
        if (importFileDto.getUploadEnd() != null) {
            upLoadEndDateGrpEl.setDataLabel(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()).format(importFileDto.getUploadEnd()));
        }
        uploadUserNameGrpEl.setDataLabel(importFileDto.getUploadUsername());
        
        if (importFileDto.getUploadEnd() != null && importFileDto.getNumFailedUploads() > 0) {
            uploadnumFailedGrpEl.setDataLabel(importFileDto.getNumFailedUploads().toString());
        } else {
            uploadnumFailedGrpEl.setDataLabel("");
        }
        
        if (importFileDto.getLastImportStart() != null) {
            lastImportStartDateGrpEl.setDataLabel(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()).format(importFileDto.getLastImportStart()));
        } else {
            lastImportStartDateGrpEl.setDataLabel("");
        }
        if (importFileDto.getLastImportEnd() != null) {
            lastImportEndDateGrpEl.setDataLabel(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()).format(importFileDto.getLastImportEnd()));
        } else {
            lastImportEndDateGrpEl.setDataLabel("");
        }
        lastImportUserNameGrpEl.setDataLabel(importFileDto.getLastImportUsername());
        
        if (importFileDto.getBulkRef() != null) {
            bulkRefRowPanel.setVisible(true);;
            bulkRefGrpEl.setDataLabel(importFileDto.getBulkRef());
        } else {
            bulkRefRowPanel.setVisible(false);
            bulkRefGrpEl.setDataLabel("");
        }
        
        if (importFileDto.getLastImportStart() != null && (importFileDto.getLastImportEnd() == null ||
                importFileDto.getLastImportEnd().before(importFileDto.getLastImportStart()))) {
            setStillBusy(MessagesUtil.getInstance().getMessage("import.file.item.view.still.busy"), true);
            parentView.checkBusySetButtons(true);       //is still busy with import
        } else {
            setStillBusy(null, false);
            parentView.checkBusySetButtons(false);             
        }
    }
    
    public void setStillBusy(String text, boolean visible) {
        if (text != null) {
            stillBusyText.setText(text);
        } else {
            stillBusyText.setText("");
        }
        stillBusyText.setVisible(visible);
    }
    
    public void setRegReadReminderVisibility(boolean visible) {
        regReadReminder.setVisible(visible);
    }

    public void setWarnMaxLicenseExceeded(String fileType, int licenseCount, int currentCount, int numItems) {
        if (fileType.equals(MeterMngStatics.FILETYPE_METERCUSTUP_BULK_IMPORT)
                && licenseCount > 0
                && currentCount+numItems > licenseCount) {
            warnMaxLicenseExceeded.setHTML(MessagesUtil.getInstance().getMessage("bulk.import.license.waring",
                    new String[] {String.valueOf(numItems)}));
            warnMaxLicenseExceeded.setVisible(true);
        } else {
            warnMaxLicenseExceeded.setText("");
            warnMaxLicenseExceeded.setVisible(false);
        }
    }

}