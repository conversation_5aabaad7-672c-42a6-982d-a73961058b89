package za.co.ipay.metermng.client.view.component.usagepoint;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;

import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.UnitsTransTypeE;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import java.util.logging.Logger;

public class UsagePointUnitsAccountTransactionView extends BaseComponent implements IpayDataProviderFilter<UnitsTrans>{
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static UsagePointUnitsAccountTransactionWidgetUiBinder uiBinder = GWT.create(UsagePointUnitsAccountTransactionWidgetUiBinder.class);

    interface UsagePointUnitsAccountTransactionWidgetUiBinder extends UiBinder<Widget, UsagePointUnitsAccountTransactionView> {
    }

    @UiField(provided=true) CellTable<UnitsTrans> clltblTransactions;
    @UiField TablePager smplpgrTransactions;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField SimpleForm form;
    @UiField Button btnExportCsv;

    private UsagePointUnitsAccountAdjustmentPanel usagePointUnitsAccountAdjustmentPanel;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private Column<UnitsTrans, Date> enteredDateCol;
    private IPayDataProvider<UnitsTrans> dataProvider = new IPayDataProvider<UnitsTrans>(this);
    private ArrayList<UnitsTrans> theTransactiondata;
    ListHandler<UnitsTrans> columnSortHandler;
    private Long unitsAccountId;
    private static Logger logger = Logger.getLogger(UsagePointUnitsAccountTransactionView.class.getName());


    public UsagePointUnitsAccountTransactionView(UsagePointWorkspaceView usagePointWorkspaceView,
            ClientFactory clientFactory, Long unitsAccountId) {
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.clientFactory = clientFactory;
        this.unitsAccountId = unitsAccountId;
        setLatestUnitTransId(usagePointWorkspaceView.getUsagePointData());
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initView();
        String resourceSymbol = MeterMngClientUtils.getServiceResourceSymbol(
                usagePointWorkspaceView.getUsagePointData().getUpPricingStructureData().getPricingStructure().getServiceResourceId());
        initTable(resourceSymbol);
        if (checkPermissions()) {
            initForm(resourceSymbol);
        } else {
            form.removeFromParent();
        }
    }

    private void initView() {
        Messages messages = MessagesUtil.getInstance();
        filterDropdown.addItem(messages.getMessage("customer.txn.trans.date"));
        filterDropdown.addItem(messages.getMessage("units.transaction.type"));
        filterDropdown.addItem(messages.getMessage("customer.txn.our.ref"));
    }

    protected void createTable() {
        clltblTransactions = new CellTable<UnitsTrans>(DEFAULT_PAGE_SIZE,
                ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initTable(String resourceSymbol) {
        // Date entered
        DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        enteredDateCol = new Column<UnitsTrans, Date>(dateCell) {
            @Override
            public Date getValue(UnitsTrans data) {
                Format format = FormatUtil.getInstance();
                return format.parseDateTime(format.formatDateTime(data.getDateEntered()));
            }
        };
        enteredDateCol.setSortable(true);
        enteredDateCol.setDefaultSortAscending(false);

        // user entered
        TextColumn<UnitsTrans> userEnteredCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return data.getUserRecEntered();
            }
        };

        final Messages messages = MessagesUtil.getInstance();
        // Transaction Type
        TextColumn<UnitsTrans> tranTypeCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return messages.getMessage("units.transaction.type."
                        + UnitsTransTypeE.fromId(data.getUnitsTransTypeId()).name().toLowerCase());
            }
        };

        // Transaction Date
        Column<UnitsTrans, Date> transDateCol = new Column<UnitsTrans, Date>(dateCell) {
            @Override
            public Date getValue(UnitsTrans data) {
                return data.getTransDate();
            }
        };
        transDateCol.setSortable(true);

        // Comment
        TextColumn<UnitsTrans> commentCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return data.getComment();
            }
        };

        // Account Ref
        TextColumn<UnitsTrans> ourRefCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return data.getOurRef();
            }
        };

        TextColumn<UnitsTrans> receiptNumCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return data.getReceiptNum();
            }
        };

        // Amount
        TextColumn<UnitsTrans> amtCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                return data.getUnitsAmt().setScale(1, RoundingMode.HALF_UP).toPlainString();
            }
        };
        amtCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);

        // Balance
        TextColumn<UnitsTrans> balCol = new TextColumn<UnitsTrans>() {
            @Override
            public String getValue(UnitsTrans data) {
                BigDecimal lowBal = data.getResultantBalance();
                if (lowBal != null) {
                    return data.getResultantBalance().setScale(1, RoundingMode.HALF_UP).toPlainString();
                } else {
                    return null;
                }
            }
        };
        balCol.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);

        clltblTransactions.addColumn(enteredDateCol, messages.getMessage("customer.txn.ent.date"));
        clltblTransactions.addColumn(userEnteredCol, messages.getMessage("customer.txn.user"));
        clltblTransactions.addColumn(tranTypeCol, messages.getMessage("units.transaction.type"));
        clltblTransactions.addColumn(transDateCol, messages.getMessage("customer.txn.trans.date"));
        clltblTransactions.addColumn(commentCol, messages.getMessage("customer.txn.comment"));
        clltblTransactions.addColumn(ourRefCol, messages.getMessage("customer.txn.our.ref"));
        clltblTransactions.addColumn(receiptNumCol, messages.getMessage("usagepoint.txn.receipt"));
        String symbolSuffix = " (" + resourceSymbol + ")";
        clltblTransactions.addColumn(amtCol, messages.getMessage("usagepoint.txn.amt") + symbolSuffix);
        clltblTransactions.addColumn(balCol, messages.getMessage("customer.txn.bal") + symbolSuffix);

        dataProvider.addDataDisplay(clltblTransactions);
        smplpgrTransactions.setDisplay(clltblTransactions);

        columnSortHandler = new ListHandler<UnitsTrans>(dataProvider.getList());
        columnSortHandler.setComparator(enteredDateCol, new Comparator<UnitsTrans>() {
            public int compare(UnitsTrans o1, UnitsTrans o2) {
                if (o1 == o2) {
                    return 0;
                }
                if (o1 != null) {
                    return (o2 != null) ? o1.getDateEntered().compareTo(o2.getDateEntered()) : 1;
                }
                return -1;
            }
        });
        clltblTransactions.addColumnSortHandler(columnSortHandler);
        clltblTransactions.getColumnSortList().push(enteredDateCol);
        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
        dateFilter.setDataProvider(dataProvider);
        handleFilterDropdownSelection(null);
    }

    public void clearTransactionTable() {
        dataProvider.setList(new ArrayList<UnitsTrans>());
        dataProvider.refresh();
    }

    private void getUnitsAccountTransactionList() {
        clientFactory.getUsagePointRpc().getUnitsAccountTransactions(unitsAccountId,
                new ClientCallback<ArrayList<UnitsTrans>>() {
                    @Override
                    public void onSuccess(ArrayList<UnitsTrans> result) {
                        if (result != null) {
                            setUnitsAccountTransactionList(result);
                        }
                    }
                });
    }

    public void setUnitsAccountTransactionList(ArrayList<UnitsTrans> thedata) {
        theTransactiondata = thedata;
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
        clltblTransactions.setPageStart(0);
    }

    // ------------------------------------------------------------------------------------------------------------
    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        String filterText = txtbxfilter.getText();
        if (filterText.trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(filterText);
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
        clltblTransactions.setPageStart(0);
    }

    @Override
    public boolean isValid(UnitsTrans value, String filter) {
        clltblTransactions.setPageStart(0);
        String selectedFilter = filterDropdown.getSelectedValue();
        Messages messagesInstance = MessagesUtil.getInstance();
        if (selectedFilter.equals(messagesInstance.getMessage("customer.txn.trans.date"))) {
            return dateFilter.isValid(value.getTransDate(), filter);
        }
        if (selectedFilter.equals(messagesInstance.getMessage("units.transaction.type"))) {
            return messagesInstance
                    .getMessage("units.transaction.type."
                            + UnitsTransTypeE.fromId(value.getUnitsTransTypeId()).name().toLowerCase())
                    .toLowerCase().contains(filter.toLowerCase());
        }
        if (selectedFilter.equals(messagesInstance.getMessage("customer.txn.our.ref"))) {
            return value.getOurRef().toLowerCase().contains(filter.toLowerCase());
        } else {
            return true;
        }
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        txtbxfilter.setText("");
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("customer.txn.trans.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }

    private void initForm(String resourceSymbol) {
        usagePointUnitsAccountAdjustmentPanel = new UsagePointUnitsAccountAdjustmentPanel(form, clientFactory);
        form.setHasDirtyDataManager(usagePointWorkspaceView);
        form.getFormFields().add(usagePointUnitsAccountAdjustmentPanel);
        final Messages messages = MessagesUtil.getInstance();
        form.getFormPanel().setHeadingText(messages.getMessage("customer.txn.input"));

        final Button saveBtn = form.getSaveBtn();
        saveBtn.setText(messages.getMessage("button.update"));
        saveBtn.ensureDebugId("saveButton");
        final Button otherBtn = form.getOtherBtn();
        saveBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                if (unitsAccountId == null) {
                    Dialogs.displayInformationMessage(
                            messages.getSavedMessage(new String[] { messages.getMessage("customer.txn.no.agreement") }),
                            MediaResourceUtil.getInstance().getInformationIcon(), saveBtn.getAbsoluteLeft(),
                            saveBtn.getAbsoluteTop(), messages.getMessage("button.close"));
                    return;
                }
                UnitsTrans accountTrans = mapFormToData();
                if (isValid(accountTrans)) {
                    saveBtn.setEnabled(false);
                    otherBtn.setVisible(false);
                    saveAdjustment(accountTrans);
                }
            }
        });
        otherBtn.setText(messages.getMessage("button.cancel"));
        otherBtn.ensureDebugId("cancelButton");
        otherBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                form.checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            form.setDirtyData(false);
                            usagePointUnitsAccountAdjustmentPanel.clearFields();
                            usagePointUnitsAccountAdjustmentPanel.clearErrors();
                        }
                    }
                });
            }
        });
        usagePointUnitsAccountAdjustmentPanel.unitsSymbolLabel.setText(resourceSymbol);
    }

    private void setLatestUnitTransId(final UsagePointData usagePointData) {
        if (usagePointData != null) {
            clientFactory.getSearchRpc().setLatestUnitTransId(usagePointData, new AsyncCallback<HistoryData>() {
                @Override
                public void onSuccess(HistoryData historyData) {
                    usagePointData.setHistoryData(historyData);
                }

                @Override
                public void onFailure(Throwable caught) {
                }
            });
        } else {
            logger.severe("UsagePointData is null");
        }

    }

    public void saveAdjustment(final UnitsTrans unitsTrans) {
        final Button saveBtn = form.getSaveBtn();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getSearchRpc().checkUsagePointDataIntegrity(usagePointWorkspaceView.getUsagePointData(),
                        new ClientCallback<Boolean>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                            @Override
                            public void onSuccess(Boolean isValid) {
                                if (isValid) {
                                    proceedWithSaving(unitsTrans, saveBtn);
                                } else {
                                    Place place = usagePointWorkspaceView.getPlace();
                                    usagePointWorkspaceView.processInvalidState(place);
                                    resetButtons();
                                }
                            }

                            @Override
                            public void onFailureClient() {
                                resetButtons();
                            }
                        }
                );
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void proceedWithSaving(UnitsTrans unitsTrans, Button saveBtn) {
        clientFactory.getUsagePointRpc().inputUnitsAccountAdjustment(
                unitsTrans,
                new ClientCallback<BigDecimal>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                    @Override
                    public void onSuccess(BigDecimal result) {
                        if (result != null) {
                            successfulAdjustment(result);
                        }
                    }
                    @Override
                    public void onFailureClient() {
                        resetButtons();
                    }
                }
        );
    }

    public void successfulAdjustment(BigDecimal result) {
        usagePointUnitsAccountAdjustmentPanel.clearFields();
        resetButtons();
        // update table
        getUnitsAccountTransactionList();
        dataProvider.refresh();
        setLatestUnitTransId(usagePointWorkspaceView.getUsagePointData());

        Button saveBtn = form.getSaveBtn();
        Messages messages = MessagesUtil.getInstance();
        Dialogs.displayInformationMessage(
                messages.getSavedMessage(new String[] { messages.getMessage("customer.txn.successful.adjustment") }),
                MediaResourceUtil.getInstance().getInformationIcon(), saveBtn.getAbsoluteLeft(),
                saveBtn.getAbsoluteTop(), messages.getMessage("button.close"));
        clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED,
                MeterMngStatics.UNITS_ACCOUNT_BALANCE_ADJUSTED, result));
    }

    private void resetButtons() {
        form.getSaveBtn().setEnabled(true);
        form.getOtherBtn().setVisible(true);
    }

    public UnitsTrans mapFormToData() {
        UnitsTrans unitsTrans = new UnitsTrans();
        unitsTrans.setUnitsAccountId(unitsAccountId);
        unitsTrans.setUsagePointId(usagePointWorkspaceView.getUsagePoint().getId());
        unitsTrans.setOurRef(usagePointUnitsAccountAdjustmentPanel.getOurRef());
        unitsTrans.setUnitsTransTypeId(UnitsTransTypeE.MANUALADJ.getId());
        Date now = new Date();
        unitsTrans.setTransDate(now);
        unitsTrans.setDateEntered(now);
        unitsTrans.setAccountRef(usagePointUnitsAccountAdjustmentPanel.getAccRef());
        unitsTrans.setComment(usagePointUnitsAccountAdjustmentPanel.getComment());
        unitsTrans.setUnitsAmt(usagePointUnitsAccountAdjustmentPanel.getAmount());
        unitsTrans.setUserRecEntered(clientFactory.getUser().getUserName());
        return unitsTrans;
    }

    public boolean isValid(UnitsTrans unitsTrans) {
        boolean valid = true;
        usagePointUnitsAccountAdjustmentPanel.clearErrors();
        if (unitsTrans.getOurRef().trim().isEmpty()) {
            usagePointUnitsAccountAdjustmentPanel.ourRefElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.ourref.null"));
            valid = false;
        }
        BigDecimal amount = unitsTrans.getUnitsAmt();
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            usagePointUnitsAccountAdjustmentPanel.amtElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("amount.cannot.be.zero"));
            valid = false;
        }
        return valid;
    }

    public boolean checkPermissions() {
        return clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_UP_UNITS_ACC_ADJ);
    }

    public void refreshUnitsAccountTransactionTable() {
        clearTransactionTable();
        getUnitsAccountTransactionList();
        dataProvider.refresh();
    }

    @UiHandler("btnExportCsv")
    void handleExportCsvButton(ClickEvent clickEvent) {
        if (!theTransactiondata.isEmpty()) {
            String filterValue = dataProvider.getFilter();
            if (filterValue == null) {
                filterValue = "";
            }
            String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                    .withTargetUrl("usagepointunitsaccounttransexport")
                    .addParam("unitsaccountid", unitsAccountId.toString())
                    .addParam("filenameprefix", usagePointWorkspaceView.getUsagePointData().getName())
                    .addParam("filterelement", filterDropdown.getItemText(filterDropdown.getSelectedIndex()))
                    .addParam("filtervalue", filterValue).toEncodedUrl();
            if (encodedUrl != null) {
                Window.open(encodedUrl, "_blank", "width=100, height=100, top=" + (Window.getClientHeight() / 2 - 50)
                        + ", left=" + (Window.getClientWidth() / 2 - 50));
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error.nodata"),
                    MediaResourceUtil.getInstance().getErrorIcon());
        }
    }

    public void setUnitsAccountId(Long unitsAccountId) {
        this.unitsAccountId = unitsAccountId;
    }
}
