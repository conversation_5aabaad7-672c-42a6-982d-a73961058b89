package za.co.ipay.metermng.client.view.component.usagepoint;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.user.client.ui.FlowPanel;
import org.moxieapps.gwt.highcharts.client.Axis;
import org.moxieapps.gwt.highcharts.client.Chart;
import org.moxieapps.gwt.highcharts.client.ChartSubtitle;
import org.moxieapps.gwt.highcharts.client.ChartTitle;
import org.moxieapps.gwt.highcharts.client.Legend;
import org.moxieapps.gwt.highcharts.client.Series;
import org.moxieapps.gwt.highcharts.client.ToolTip;
import org.moxieapps.gwt.highcharts.client.ToolTipData;
import org.moxieapps.gwt.highcharts.client.ToolTipFormatter;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.shared.MeterMngStatics;

import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

public class UsagePointRechargeChart extends BaseComponent {
        
    private Long usagePointId;
    private FlowPanel panel = new FlowPanel();
    private ArrayList<CustomerTransAlphaData> trData = new ArrayList<>();
    private Chart chart;
    
    private static Logger logger = Logger.getLogger(UsagePointRechargeChart.class.getName());
    
    public Long getUsagePointId() {
        return usagePointId;
    }

    public void setUsagePointId(Long upId) {
        this.usagePointId = upId;
    }

    String title = MessagesUtil.getInstance().getMessage("usagepoint.recharge.title");
    
    public UsagePointRechargeChart() {
        panel.setWidth("100%");
        initWidget(panel);
    }
    
    public void populateTransactionData(ArrayList<CustomerTransAlphaData> transactions) {
        trData = transactions;
        clearChart();
        createChart();
    }
    
    private void createChart() {        
        Date startDate = null;
        Date endDate = null;
        double max = 0.0;
        double price = 0.0;
        double purchasePrice = 0.0;
        Number[][] currencyPoints = new Number[trData.size()][2];
//        Number[][] purchasePricePoints = new Number[trData.size()][2];
        CustomerTrans trans = null;
        //Going backwards through the data as its from newest to oldest
        for (int i=trData.size()-1;i>=0;i--) {
            trans = trData.get(i);
            if (startDate == null || trans.getTransDate().before(startDate)) {
                startDate = trans.getTransDate();
            }
            if (endDate == null || trans.getTransDate().after(endDate)) {
                endDate = trans.getTransDate();
            }                        
            if (max < price) {
                max = price;
            }
            if (max < purchasePrice) {
                max = purchasePrice;
            }
              price = trans.getAmtInclTax().doubleValue();
              purchasePrice = trans.getAmtInclTax().doubleValue() - trans.getAmtTax().doubleValue();
              currencyPoints[i][1] = price;
              currencyPoints[i][0] = trans.getTransDate().getTime();
//              purchasePricePoints[i][1] = purchasePrice;
//              purchasePricePoints[i][0] = trans.getTransDate().getTime();
        }
        
        if (trData != null && !trData.isEmpty()) {
            final DateTimeFormat dtf = DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat());
            chart = new Chart()  
            .setType(Series.Type.SCATTER)  
            .setZoomType(Chart.ZoomType.X)  
            .setChartTitle(new ChartTitle().setText(MessagesUtil.getInstance().getMessage("usagepoint.recharge.chart.title")))  
            .setChartSubtitle(new ChartSubtitle().setText(MessagesUtil.getInstance().getMessage("usagepoint.recharge.chart.subtitle")))  
             .setToolTip(
                     new ToolTip().setShared(false)
    //                               .setPointFormat("<span style=\"color:{series.color}\">{series.name}</span>: <b>{point.y}</b> ({point.change}%)<br/>"));  
                                   .setFormatter(new ToolTipFormatter() {  
                        public String format(ToolTipData toolTipData) {  
                            String name = (toolTipData.getSeriesName() != null ? toolTipData.getSeriesName() : "");
                            return dtf.format(new Date(toolTipData.getXAsLong())) 
                                    + " <br/><span style=\"color:{series.color}\">"+name+": " + toolTipData.getYAsDouble()+"</span>";                        
                        }  
                    })  
                )  
            .setLegend(new Legend()  
                .setLayout(Legend.Layout.VERTICAL)  
                .setAlign(Legend.Align.RIGHT)  
                .setVerticalAlign(Legend.VerticalAlign.TOP)  
                .setX(-10)  
                .setY(100)  
                .setBorderWidth(1)  
            );  
    
            chart.getXAxis()
            .setType(Axis.Type.DATE_TIME)
            .setMaxZoom(MeterMngStatics.ONE_HOUR)
            .setAxisTitleText(MessagesUtil.getInstance().getMessage("usagepoint.recharge.chart.xtitle"))
            .setMin(startDate.getTime() - MeterMngStatics.ONE_DAY);
    
            chart.getYAxis()  
            .setAxisTitleText(MessagesUtil.getInstance().getMessage("usagepoint.recharge.chart.ytitle"))  
            .setMin(-10)
            .setMax(max + 50);
            
            Series series = chart.createSeries().setName(MessagesUtil.getInstance().getMessage("usagepoint.recharge.chart.price"));
            series.setPoints(currencyPoints);
            chart.addSeries(series);
            
    //        series = chart.createSeries().setName(MessagesUtil.getInstance().getMessage("meterrecharge.chart.purchaseprice"));
    //        series.setPoints(purchasePricePoints);        
    //        chart.addSeries(series);
            
            panel.add(chart);
        }
    }
    
    private void clearChart() {  
        if (chart != null) {
            panel.remove(chart);
            chart = null;
        }
    }
}
