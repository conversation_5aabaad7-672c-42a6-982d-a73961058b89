package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.TariffBulkImportDialogueBox;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class TariffBulkImportHelper extends BaseFiletypeHelper {

    public TariffBulkImportHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }
    
   @Override
   public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
       return new TariffBulkImportDialogueBox(clientFactory, parent);
   }
   
   @Override
   public String getPricingStructureNameColumnValue(ImportFileItemDto object) {
       return object.getTariffPsImportRecord().getPricingStructureNameDONOTCHANGE();
   }
   
   @Override
   public String getTariffNameColumnValue(ImportFileItemDto object) {
       return object.getTariffPsImportRecord().getTariffName();
   }
   
   @SuppressWarnings("unchecked")
@Override
   public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
       table.addColumn(tableColumnMap.get("pricingStructureNameCol"), messagesInstance.getMessage("import.ps.name.label"));  
       table.addColumn(tableColumnMap.get("tariffNameCol"), messagesInstance.getMessage("import.tariff.name.label")); 
   }
}
