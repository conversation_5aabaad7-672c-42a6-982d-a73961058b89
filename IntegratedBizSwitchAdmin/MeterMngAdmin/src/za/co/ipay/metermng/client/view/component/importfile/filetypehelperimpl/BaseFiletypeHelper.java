package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.client.ui.Button;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.importfile.FileDetailPanel;
import za.co.ipay.metermng.client.view.component.importfile.IFileTypeHelper;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class BaseFiletypeHelper extends BaseComponent implements IFileTypeHelper {
    
    protected ImportFileItemView parent;
    protected DateTimeFormat dfFormat = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss Z");
    protected Messages messagesInstance = MessagesUtil.getInstance();

    public BaseFiletypeHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        this.clientFactory = clientfactory;
        this.parent = parent;
    }
    
    @Override
    public void setExtractBtn(Button extractBtn, boolean isExtractAvailable) {
        extractBtn.setVisible(false);
    }
    
    @Override
    public void setSpareBtn(Button spareBtn) {
        spareBtn.setVisible(false);
    }

    @Override
    public void setParent(ImportFileItemView parent) {
        this.parent = parent;
    }
    
    @Override
    public void generateCsvDownload(Logger logger, ImportFileDto importFileDto) {
    }
    
    @Override
    public void handleSpareBtn(Logger logger, ImportFileDto importFileDto) {
    }
    
   @Override
   public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
       return new ImportFileItemBaseDialogueBox(clientFactory, parent);
   }
   
   @Override
   public void setRegReadingsMsgVisible(FileDetailPanel fileDetailPanel) {
       fileDetailPanel.setRegReadReminderVisibility(false);
   }
   
   @Override
   public String getMeterColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public String getUsagePointColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public String getAgrRefColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public String getChannelValueColumnValue(ImportFileItemDto object) {
       return null;
   }
    
   @Override
   public String getReadingTimeStampColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public String getPricingStructureNameColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public String getTariffNameColumnValue(ImportFileItemDto object) {
       return null;
   }
   
   @Override
   public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
   }
   
   @Override
   public String getWaitingText() {
       return null;    
   }
   
    //---------------------------------------------------------------------------------------------------
    protected void doDownload(final Logger logger, ImportFileDto importFileDto, String fileNameSuffix, String csvContent) {
        if (csvContent != null && !csvContent.isEmpty()) {
            int positionFileType = importFileDto.getImportFilename().lastIndexOf(".");
            String fileDate = DateTimeFormat.getFormat("yyyyMMddTHHmmss").format(new Date());
            if (fileNameSuffix == null) {
                fileNameSuffix = "";
            }
            String fileName= importFileDto.getImportFilename().substring(0, positionFileType) + fileNameSuffix + fileDate +".csv";
            try {
                genCsvDownload(csvContent, fileName);
            } catch (Exception e) {
                logger.log(Level.SEVERE,"ERROR: extractFailedItems() EXCEPTION: " + e + " : " + e.getMessage());
                Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("import.extract.items.exception"), 
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
            
        } else {
            Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("import.extract.items.non"), 
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }
        
        parent.enableButtons();
    }


    //*******************************************************************************************************
    public static native void genCsvDownload(String csvContent, String fileName) /*-{

        var download = function (content, fileName, mimeType) {
            var a = document.createElement('a');
            mimeType = mimeType || 'application/octet-stream';

            if (navigator.msSaveBlob) { // IE10
                navigator.msSaveBlob(new Blob([content], {
                    type: mimeType
                }), fileName);
            } else if (URL && 'download' in a) { //html5 A[download]
                a.href = URL.createObjectURL(new Blob([content], {
                    type: mimeType
                }));
                a.setAttribute('download', fileName);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } else {
                location.href = 'data:application/octet-stream,' + encodeURIComponent(content); // only this mime type is supported
            }
        }

        download(csvContent, fileName, 'text/csv;encoding:utf-8');
    }-*/;
}
