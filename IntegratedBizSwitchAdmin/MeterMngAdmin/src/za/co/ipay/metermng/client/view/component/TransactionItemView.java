package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.meter.TransactionItemsWidget;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.CustomerTransTypeE;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.shared.CustomerTransItemData;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 *         Created on 4/26/17.
 */
public class TransactionItemView {

    private final ClientFactory clientFactory;
    private final CustomerTrans transactionData;
    private final UsagePointWorkspaceView usagePointWorkspaceView;
    
    private Logger logger = Logger.getLogger(TransactionItemView.class.getName());

    public TransactionItemView(ClientFactory clientFactory, CustomerTrans transactionData, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.transactionData = transactionData;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
    }

    public void showMeterTransactionDetails(final Widget widget, final int leftPosition, final int topPosition) {
        clientFactory.getMeterRpc().getTransactionCustomerTransItem(transactionData.getId(), new ClientCallback<List<CustomerTransItemData>>() {
            @Override
            public void onSuccess(List<CustomerTransItemData> result) {
                hideWaitingDialog();
                if (result != null) {
                    showTransactionDetails(widget, (ArrayList<CustomerTransItemData>) result, leftPosition, topPosition);
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                super.onFailure(caught);
                hideWaitingDialog();
            }
        });
    }

    public void showTransactionDetails(final Widget widget, ArrayList<CustomerTransItemData> transactionList, final int leftPosition, final int topPosition) {
        boolean isScheduledTrans = transactionData.getCustomerTransTypeId() == CustomerTransTypeE.SCHEDULED.getId();
        TransactionItemsWidget transactionItemsWidget = new TransactionItemsWidget(usagePointWorkspaceView, widget, transactionData, clientFactory, isScheduledTrans);
        transactionItemsWidget.setMeterTransactionList(transactionList);
        transactionItemsWidget.showTransactionDetails();
    }
}
