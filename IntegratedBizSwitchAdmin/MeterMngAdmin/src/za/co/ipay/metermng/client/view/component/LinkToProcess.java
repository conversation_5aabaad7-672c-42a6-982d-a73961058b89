package za.co.ipay.metermng.client.view.component;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceOpenCallback;
import za.co.ipay.metermng.client.event.LinkToEvent;
import za.co.ipay.metermng.client.event.LinkToEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterPlace;
import za.co.ipay.metermng.client.history.UsagePointPlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.workspace.group.type.GroupTypeWorkspaceView;

import com.google.gwt.place.shared.Place;

public abstract class LinkToProcess {
    private static Logger logger = Logger.getLogger(LinkToProcess.class.getName());

    public LinkToProcess() {
        
    }
    
    public void processLinkTo(final ClientFactory clientFactory, final LinkToEvent event) {
                logger.info("LinkToEvent received: " + this);

                final String param1 = event.getLinkParamStr1();
                final int paramType = event.getLinkParamType();
                
                Place pl = null;
                if (paramType == LinkToEvent.METER_NUM) {
                    pl = new MeterPlace(param1);   
                } else {              //else is LinkToEvent.USAGE_POINT_NAME
                    pl = new UsagePointPlace(param1);
                }
                final Place thisPlace = pl;
                
                clientFactory.getWorkspaceContainer().openWorkspace(thisPlace, new WorkspaceOpenCallback() {                    
                    @Override
                    public void onWorkspaceOpened(Workspace workspace) {
                        gotoLinkTo(event, workspace);
                    }
                    
                    @Override
                    public void onWorkspaceNotOpened(Exception exception) {
                            Dialogs.displayErrorMessage("RC",
                                    MediaResourceUtil.getInstance().getErrorIcon(), 
                                    MessagesUtil.getInstance().getMessage("button.close"));
                    }
                    
                    @Override
                    public void onWorkspaceAlreadyOpen(Workspace workspace) {
                        gotoLinkTo(event, workspace);
                    }
                    
                    @Override
                    public void onWorkspaceOpenPending(Place place) {
                        logger.info(""); 
                    }
                });
    }    
    
    protected abstract void gotoLinkTo(LinkToEvent event, Workspace workspace);
}
