package za.co.ipay.metermng.client.view.component.usagepoint;

import java.math.BigDecimal;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

public class UsagePointUnitsAccountAdjustmentPanel extends SimpleFormPanel {
    @UiField FormGroupPanel usagePointUnitsAccountPanel;
    @UiField TextBox txtbxAccRef;
    @UiField TextBox txtbxOurRef;
    @UiField BigDecimalValueBox txtbxAmt;
    @UiField TextBox txtbxComment;
    @UiField FormElement accRefElement;
    @UiField FormElement ourRefElement;
    @UiField FormElement amtElement;
    @UiField FormElement commentElement;
    @UiField Label unitsSymbolLabel;

    private static CustomerAdjustmentPanelUiBinder uiBinder = GWT.create(CustomerAdjustmentPanelUiBinder.class);

    interface CustomerAdjustmentPanelUiBinder extends UiBinder<Widget, UsagePointUnitsAccountAdjustmentPanel> {
    }

    public UsagePointUnitsAccountAdjustmentPanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    public void clearFields() {
        form.setDirtyData(false);
        txtbxAccRef.setText("");
        txtbxOurRef.setText("");
        txtbxAmt.setText("");
        txtbxComment.setText("");
    }

    @Override
    public void clearErrors() {
        accRefElement.clearErrorMsg();
        ourRefElement.clearErrorMsg();
        amtElement.clearErrorMsg();
        commentElement.clearErrorMsg();
    }

    @Override
    public void addFieldHandlers() {
        FormDataChangeHandler formDataChangeHandler = new FormDataChangeHandler(form);
        txtbxAccRef.addChangeHandler(formDataChangeHandler);
        txtbxOurRef.addChangeHandler(formDataChangeHandler);
        txtbxAmt.addChangeHandler(formDataChangeHandler);
        txtbxComment.addChangeHandler(formDataChangeHandler);
    }

    public String getAccRef() {
        return txtbxAccRef.getValue();
    }

    public String getOurRef() {
        return txtbxOurRef.getValue();
    }

    public BigDecimal getAmount() {
        return txtbxAmt.getValue();
    }

    public String getComment() {
        return txtbxComment.getValue();
    }
}
