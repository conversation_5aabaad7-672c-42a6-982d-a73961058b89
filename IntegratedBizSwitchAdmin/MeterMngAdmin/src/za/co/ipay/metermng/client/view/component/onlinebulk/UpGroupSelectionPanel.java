package za.co.ipay.metermng.client.view.component.onlinebulk;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestBox.DefaultSuggestionDisplay;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListEvent;
import za.co.ipay.metermng.client.event.UpdateUsagePointGroupsListHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.ContainsUPGroupSelectionComponent;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataItemSuggestBoxTree;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataItemSuggestBoxTreeFactory;
import za.co.ipay.metermng.client.view.component.selection.SuggestBoxTreeWithButton;
import za.co.ipay.metermng.client.widget.suggestboxtree.SuggestBoxTree;
import za.co.ipay.metermng.shared.BaseMeterSuggestOracle;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.MeterSuggestion;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class UpGroupSelectionPanel extends BaseComponent implements ContainsMeterUpCustComponent, ProvidesResize, RequiresResize {

    @UiField DisclosurePanel upGroupSelectionDisclosurePanel;
    @UiField HorizontalPanel groupTypesPanel;
    @UiField Button selectBtn;
    @UiField Button clearBtn;
    @UiField FormElement suggestBoxExistingMeterElement;
    @UiField(provided=true) SuggestBox suggestBoxExistingMeterNumber;
    private BaseMeterSuggestOracle metersAssignedToUpSuggestOracle;

    private HashMap<Long, UpGenGroupLinkData> upgengroups = new HashMap<Long, UpGenGroupLinkData>();
    private ArrayList<UpGenGroupLinkData> upGenGroupLinkData = new ArrayList<UpGenGroupLinkData>();

    private Boolean disclosureOpen = null;

    private Logger logger = Logger.getLogger("UpGroupSelectionPanel");
    private ContainsUPGroupSelectionComponent parentWorkspace;
    private boolean allowEdit;
    private HasDirtyData hasDirtyData;

    private static UpGroupSelectionPanelUiBinder uiBinder = GWT.create(UpGroupSelectionPanelUiBinder.class);

    interface UpGroupSelectionPanelUiBinder extends UiBinder<Widget, UpGroupSelectionPanel> {
    }

    public UpGroupSelectionPanel(ClientFactory clientFactory, ContainsUPGroupSelectionComponent parentWorkspace, boolean allowEditData, boolean enableCopyGroups) {
        // not really tracking has dirty as this constructor is used for this panel to be used for selecting groups not for updating group on a meter
        this(clientFactory, parentWorkspace, new LocalOnlyHasDirtyData(), allowEditData, false, new ArrayList<UpGenGroupLinkData>(), enableCopyGroups);
    }

    public UpGroupSelectionPanel(ClientFactory clientFactory, ContainsUPGroupSelectionComponent parentWorkspace, HasDirtyData hasDirtyData, boolean allowEdit, boolean setSelected, ArrayList<UpGenGroupLinkData> upGenGroupLinkData, boolean enableCopyGroups) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.allowEdit = allowEdit;
        this.hasDirtyData = hasDirtyData;
        this.upGenGroupLinkData = upGenGroupLinkData;
        metersAssignedToUpSuggestOracle = new BaseMeterSuggestOracle(clientFactory);
        suggestBoxExistingMeterNumber = new SuggestBox(metersAssignedToUpSuggestOracle);
        initWidget(uiBinder.createAndBindUi(this));
        if(!enableCopyGroups) {
            suggestBoxExistingMeterElement.removeFromParent();
        }
        getGroupTypes(setSelected);
        addFieldHandlers();
    }

    private void addFieldHandlers() {
        suggestBoxExistingMeterNumber.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
    }

    private void getGroupTypes(final boolean select) {
        clientFactory.getGroupRpc().getGroupTypes(false, false, new ClientCallback<ArrayList<SelectionDataItem>>() {
            @Override
            public void onSuccess(ArrayList<SelectionDataItem> result) {
                //Display each group type in a selection widget
                logger.info("Got selectionData: "+result.size());
                Collections.sort(result, new Comparator<SelectionDataItem>() {
                    public int compare(SelectionDataItem o1, SelectionDataItem o2) {
                        if(o1.getLayoutOrder() == null && o2.getLayoutOrder() == null) {
                            return o1.getName().compareTo(o2.getName());
                        } else if (o1.getLayoutOrder() == null && o2.getLayoutOrder() != null) {
                            return 1;
                        } else if (o2.getLayoutOrder() == null && o1.getLayoutOrder() != null) {
                            return -1;
                        }

                        return o1.getLayoutOrder().compareTo(o2.getLayoutOrder());
                    }
                });

                for(int i=0;i<result.size();i++) {
                    final SelectionDataItem item = result.get(i);
                    boolean mustSelectLastLevel = item.isRequired();
                    SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = SelectionDataItemSuggestBoxTree.createSuggestBoxTree(item, mustSelectLastLevel, clientFactory, hasDirtyData, true);
                    SuggestBoxTreeWithButton sbtwB = new SuggestBoxTreeWithButton(clientFactory, sbt);
                    if (!allowEdit || !clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_GROUP_TYPE_ADMIN)) {
                        sbtwB.removeButtonPanel();
                    }
                    groupTypesPanel.add(sbtwB);
                }
                if (select) {
                    setSelectedGroups(false);
                }
                logger.info("Groups complete");
                addUpdatedHandler();
            }
        });

    }

    public void setButtonText(String msgKey) {
        selectBtn.setVisible(true);
        selectBtn.setText(MessagesUtil.getInstance().getMessage(msgKey));

        clearBtn.setVisible(true);
    }

    @UiHandler("selectBtn")
    public void handleSelectButton(ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                parentWorkspace.selectBtnProcess();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("clearBtn")
    public void handleClear(ClickEvent event) {
        clearUPGroupPanel();
        parentWorkspace.clearAll();
    }

    @UiHandler("suggestBoxExistingMeterNumber")
    void handleSuggestBox(SelectionEvent<Suggestion> se) {
        if (se.getSelectedItem() instanceof MeterSuggestion) {
            String meterNum = ((MeterSuggestion) se.getSelectedItem()).getMeter().getNumber().trim();
            ((DefaultSuggestionDisplay)suggestBoxExistingMeterNumber.getSuggestionDisplay()).hideSuggestions();
            showMeterInfoPopup(meterNum);
        }
    }

    private void showMeterInfoPopup(String meterNumber) {
        clientFactory.getSearchRpc().getUsagePointDataByMeterNum(meterNumber, new ClientCallback<UsagePointData>() {
            @Override
            public void onSuccess(UsagePointData result) {
                new MeterUpCustWidget(clientFactory, UpGroupSelectionPanel.this, result, suggestBoxExistingMeterElement.getAbsoluteLeft(), Window.getScrollTop() + 120);
            }
        });

    }

    private void clearUPGroupPanel(){
        //clear the usage point groups panel  .... the selections
        SuggestBoxTreeWithButton sbtwB;
        SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt;
        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTreeWithButton ) {
                sbtwB = (SuggestBoxTreeWithButton)groupTypesPanel.getWidget(j);
                sbt = sbtwB.getSuggestBoxTree();
                sbt.clear();
            }
        }
        suggestBoxExistingMeterNumber.setValue("");
    }

    private void mapFormToData() {
        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTreeWithButton) {
                UpGenGroupLinkData upgdata = null;
                SuggestBoxTreeWithButton sbtwB = (SuggestBoxTreeWithButton)groupTypesPanel.getWidget(j);
                SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = sbtwB.getSuggestBoxTree();
                SelectionDataItemSuggestBoxTreeFactory factory = (SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory();

                // Looking through multiple group type ids because group type potentially forms a hierarchy
                for (Long t : factory.getGroupTypeIds()) {
                    upgdata = getSelectedGroupByGroupType(t);
                    if (upgdata != null) {
                        break;
                    }
                }

                SelectionDataItem item = sbt.getCurrentSelectedItem();
                if(item == null) {
                    if(upgdata != null) {
                        // UI for this item is cleared so link must be deleted
                        upgdata.setGenGroupId(null);
                    }
                    continue;
                }

                Long selectedGroup = item.getActualId();
                if (SelectionDataItem.isActualGroupId(selectedGroup)) {
                    if (upgdata == null) {
                        upgdata = new UpGenGroupLinkData();
                        upgdata.setGroupTypeId(sbt.getTreeInfo().getGroupTypeId());
                        upgdata.setUsagePointId(null);
                    }
                    upgdata.setGenGroupId(selectedGroup);
                    upgengroups.put(upgdata.getGroupTypeId(),upgdata);
                } else {
                    if (upgdata != null) {
                        upgdata.setGenGroupId(null);
                        upgengroups.put(upgdata.getGroupTypeId(),upgdata);
                    }
                }
            }
        }
    }

    private void setSelectedGroups(boolean copiedFromOtherMeter) {
        if (upGenGroupLinkData != null && !upGenGroupLinkData.isEmpty()) {
            UpGenGroupLinkData uData;
            upgengroups = new HashMap<Long, UpGenGroupLinkData>(upGenGroupLinkData.size());
            for (UpGenGroupLinkData gg: upGenGroupLinkData) {
                upgengroups.put(gg.getGroupTypeId(), gg);
            }

            for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
                if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTreeWithButton ) {
                    SuggestBoxTreeWithButton sbtwB = (SuggestBoxTreeWithButton)groupTypesPanel.getWidget(j);
                    SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = sbtwB.getSuggestBoxTree();
                    SelectionDataItemSuggestBoxTreeFactory factory = (SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory();
                    if(!copiedFromOtherMeter) {
                        sbt.clear();
                    }
                    for (Long t : factory.getGroupTypeIds()) {
                        uData = getSelectedGroupByGroupType(t);
                        if (uData != null) {
                            // don't modify the original depth list or we may end up modifying it again
                            ArrayList<Long> depthList = new ArrayList<Long>(uData.getDepthList());
                            if(t != sbt.getTreeInfo().getGroupTypeId()) {
                                // this is a sub group type node
                                depthList.add(0, t);
                            }
                            sbt.setValue(depthList);
                            sbtwB.getElement().focus();
                            suggestBoxExistingMeterNumber.setText("");
                            break;
                        } else {
                            if(!copiedFromOtherMeter) {
                                sbt.clear();
                            }
                        }
                    }

                }
            }
        } else {
            //ex mapDataToForm --> after a save, if the groups were incomplete, did NOT save the usage Point groups, clear screen here
            clearUPGroupPanel();
        }
    }

    private UpGenGroupLinkData getSelectedGroupByGroupType(Long groupTypeId) {
        if (upgengroups == null)
            return null;
        return upgengroups.get(groupTypeId);
    }

    private ArrayList<UpGenGroupLinkData> getSelectedGroupsList() {
        if (upgengroups == null) {
            return null;
        }
        ArrayList<UpGenGroupLinkData> thelist = new ArrayList<UpGenGroupLinkData>(upgengroups.values());
        return thelist;
    }

    public ArrayList<UpGenGroupLinkData> getSelectedGroups() {
        upgengroups = new HashMap<Long, UpGenGroupLinkData>();
        mapFormToData();
        return getSelectedGroupsList();
    }

    public HashMap<Long, UpGenGroupLinkData> getNewUpGenGroupsMap() {
        upgengroups = new HashMap<Long, UpGenGroupLinkData>();
        mapFormToData();
        return upgengroups;
    }

    public HashMap<Long, UpGenGroupLinkData> getUpGenGroupsMap() {
        mapFormToData();
        return upgengroups;
    }

    public boolean areGroupsAllSelected() {
        boolean validated = true;

        for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
            if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTreeWithButton) {
                SuggestBoxTreeWithButton sbtwB= (SuggestBoxTreeWithButton) groupTypesPanel.getWidget(j);
                SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = sbtwB.getSuggestBoxTree();
                if (!sbt.getTreeInfo().isRequired()) {
                    continue;
                }
                SelectionDataItem selectedItem = sbt.getCurrentSelectedItem();
                if (selectedItem == null || ! SelectionDataItem.isActualGroupId(selectedItem.getActualId())) {
                    sbt.setError(MessagesUtil.getInstance().getMessage("usagepoint.group.required"));
                    validated = false;
                }
            }
        }
        return validated;
    }

    public void setOpen(boolean isOpen) {
        if (disclosureOpen == null) {
            upGroupSelectionDisclosurePanel.setOpen(isOpen);
        } else {
            upGroupSelectionDisclosurePanel.setOpen(disclosureOpen);
        }
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                upGroupSelectionDisclosurePanel.setWidth("100%");
            }
        }.schedule(100);

    }

    @Override
    public void setSelectedGroups(ArrayList<UpGenGroupLinkData> upGenGroupLinkData, boolean copiedFromOtherMeter) {
        this.upGenGroupLinkData = upGenGroupLinkData;
        setSelectedGroups(copiedFromOtherMeter);
    }

    private void addUpdatedHandler() {
        clientFactory.getEventBus().addHandler(UpdateUsagePointGroupsListEvent.TYPE, new UpdateUsagePointGroupsListHandler() {

            @Override
            public void updateUsagePointGroupList(final UpdateUsagePointGroupsListEvent event) {
                Long groupTypeId = event.getGroupTypeId();
                if(groupTypeId == null) {
                    logger.info("No group type id for UpdateUsagePointGroupsListEvent: " + event);
                    return;
                }
                for (int j=0; j<groupTypesPanel.getWidgetCount(); j++) {
                    if (groupTypesPanel.getWidget(j) instanceof SuggestBoxTreeWithButton ) {
                        SuggestBoxTreeWithButton sbtwB = (SuggestBoxTreeWithButton)groupTypesPanel.getWidget(j);
                        final SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt = sbtwB.getSuggestBoxTree();
                        final SelectionDataItemSuggestBoxTreeFactory factory = (SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory();
                        if(factory.getGroupTypeIds().contains(event.getGroupTypeId())) {
                            if(event.getSelected() != null) {
                                clientFactory.getGroupRpc().getPath(event.getSelected(), new ClientCallback<ArrayList<Long>>() {

                                    @Override
                                    public void onSuccess(final ArrayList<Long> path) {
                                        boolean hasGroupTypeHierarchy = ((SelectionDataItemSuggestBoxTreeFactory) sbt.getFactory()).initialItemHasGroupTypeHierarchy();
                                        if(hasGroupTypeHierarchy) {
                                            path.add(0, event.getGroupTypeId());
                                        }
                                        sbt.reload(path);
                                    }
                                });
                            }
                        }
                    }
                }

            }
        });

    }

}
