package za.co.ipay.metermng.client.view.component.importfile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.cell.client.ActionCell;
import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.AsyncHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.ColumnSortList.ColumnSortInfo;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.DockLayoutPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.CellPreviewEvent.Handler;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.importfile.ImportFileWorkspaceView;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileActionParamsDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileDto;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileListDto;

public class ImportFileView extends BaseComponent implements FormManager<ImportFileDto> {
    
    private static final int DEFAULT_PAGE_SIZE = 15;
    
    @UiField DockLayoutPanel mainPanel;
    @UiField PageHeader pageHeader;
	
	@UiField(provided=true) FileImportUploadPanel fileUploadPanel;
	
	@UiField VerticalPanel paramsPanel;
	
	@UiField HTML dataName;
    @UiField HTMLPanel tablePanel;	
    
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateBox filterDatebox;
    
    @UiField(provided=true) CellTable<ImportFileDto> table;
    @UiField TablePager pager;

    private AsyncDataProvider<ImportFileDto> dataProvider;
    private AsyncHandler columnSortHandler;
    
    private Integer totalResults;
    private int filterTableColIndx;
    private String filterColumnName;
    private String filterString;
    private Date filterDate;
    
    private TextColumn<ImportFileDto> filenameCol;
    private TextColumn<ImportFileDto> numItemsCol;
    private Column<ImportFileDto, Date> uploadStartDateCol;
    private Column<ImportFileDto, Date> uploadEndDateCol;
    private TextColumn<ImportFileDto> uploadFailuresCol;
    private TextColumn<ImportFileDto> lastImportedByCol;
    
    private ImportFileWorkspaceView parentWorkspace;
    private static Logger logger = Logger.getLogger(ImportFileView.class.getName());

    private static ImportFileViewUiBinder uiBinder = GWT.create(ImportFileViewUiBinder.class);

    interface ImportFileViewUiBinder extends UiBinder<Widget, ImportFileView> {
    }

	public ImportFileView(ImportFileWorkspaceView parentWorkspace, ClientFactory clientFactory) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        fileUploadPanel = new FileImportUploadPanel(clientFactory, this);
        initTable();
        
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }
	
	protected void submitFileUploadPreProcess() {
	    tablePanel.setVisible(false);
	}
    protected void submitFileUploadPostProcess(boolean success) {
        tablePanel.setVisible(true);
        refreshTable();
    }
    
    protected FileImportUploadPanel getFileImportUploadPanel() {
        return fileUploadPanel;
    }
    
    protected void loadActionParams(final String typeClass, final String fileName, final String uploadedMessage, final boolean canSubmit, final boolean inputFileUploaded) {
        //to catch when table has input_file setting that is not implemented in code 
        //and avoid making an unnecessary entry into import_File for dummy files
        String codeCheckImplementedFileSetting = ActionParamsPanel.actionPanelsMap.get(typeClass);
        if (codeCheckImplementedFileSetting == null 
                || (codeCheckImplementedFileSetting.equals("y") && !inputFileUploaded)) {
            Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("import.file.no.parameters.or.setting", new String[] {typeClass, codeCheckImplementedFileSetting}), 
                    new Image(MediaResourceUtil.getInstance().getErrorIcon()), 
                    StyleNames.POPUP_MESSAGE, 
                    MessagesUtil.getInstance().getMessage("button.close"), null,
                    false, true);
            reset();
            return;
        }
        
        final ImportFileView thisView = this;
        clientFactory.getImportFileDataRpc().getImportFileParamData(clientFactory.getUser().getUserName(), typeClass, fileName, new ClientCallback<ImportFileActionParamsDto>() {
            @Override
            public void onSuccess(ImportFileActionParamsDto result) {
                if (result == null) {
                    Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("import.file.get.params.fail", new String[] {typeClass}), 
                            new Image(MediaResourceUtil.getInstance().getErrorIcon()), 
                            StyleNames.POPUP_MESSAGE, 
                            MessagesUtil.getInstance().getMessage("button.close"), null,
                            false, true);
                    reset();
                    return;
                }
                
                final ActionParamsPanel actionParamsPanel = new ActionParamsPanel(clientFactory, thisView, typeClass, result, canSubmit, inputFileUploaded);
                if (actionParamsPanel.getTypeClassPanel() == null) {
                    Dialogs.centreMessage(MessagesUtil.getInstance().getMessage("import.file.no.parameters", new String[] {typeClass}), 
                            new Image(MediaResourceUtil.getInstance().getErrorIcon()), 
                            StyleNames.POPUP_MESSAGE, 
                            MessagesUtil.getInstance().getMessage("button.close"), null,
                            false, true);
                    reset();
                    return;
                } else if (actionParamsPanel.getTypeClassPanel().getErrorMessage() != null) {
                    Dialogs.centreMessage(actionParamsPanel.getTypeClassPanel().getErrorMessage(), 
                            new Image(MediaResourceUtil.getInstance().getErrorIcon()), 
                            StyleNames.POPUP_MESSAGE, 
                            MessagesUtil.getInstance().getMessage("button.close"), null,
                            false, true);
                    reset();
                    return;
                } else {
                    paramsPanel.clear();
                    paramsPanel.add(actionParamsPanel);
                    paramsPanel.setVisible(true);
                    Scheduler.get().scheduleDeferred(new ScheduledCommand() {    
                        @Override
                        public void execute() {
                            refreshTable();
                        }
                    });
                    //if original fileName is null - i.e. is a params only bulk change; then display the generated filename as well
                    String finalMessage = uploadedMessage;
                    if (finalMessage != null) {
                        if (fileName == null) {
                            finalMessage += MessagesUtil.getInstance().getMessage("import.file.param.no.data.dummy.filename", new String[] {result.getImportFile().getImportFilename()});
                        }
                        Dialogs.centreMessage(finalMessage, new Image(MediaResourceUtil.getInstance().getInformationIcon()), 
                                StyleNames.POPUP_MESSAGE, 
                                MessagesUtil.getInstance().getMessage("button.close"), null,
                                false, true);
                    }
                }
            }
        });
    }
    
    public void reset() {
        refreshTable();     //to catch the latest import status
        paramsPanel.clear();
        paramsPanel.setVisible(false);     //close param panel if was open
        fileUploadPanel.clearAll();
    }
    
    public void onArrival(Place place) {        
        logger.info("Refreshing table ");
        refreshTable();
    }
	
	private void initTable() {
        table = new CellTable<ImportFileDto>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }
	
    private void initUi() {
        pageHeader.setHeading(MessagesUtil.getInstance().getMessage("import.upload.header"));
        dataName.setText(MessagesUtil.getInstance().getMessage("import.upload.uploaded.files.title"));
        initFilters();
        createTable();
        loadTable();
        paramsPanel.setVisible(false);
    }

    private void initFilters() {
        filterDropdown.addItem("");
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.upload.file.name.label"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.upload.startdate.label"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("import.upload.last.imported.by.label"));
        
        filterDatebox
                .setFormat(new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateFormat())));
        filterDatebox.getTextBox().addKeyUpHandler(new KeyUpHandler() {
            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleDateFilter();
            }
        });
    }
    
	private void createTable() {
	    filenameCol = new TextColumn<ImportFileDto>() {
            @Override
            public String getValue(ImportFileDto object) {
                return object.getImportFilename();
            }
        };
        filenameCol.setSortable(true);
        filenameCol.setDefaultSortAscending(true);
        
        numItemsCol = new TextColumn<ImportFileDto>() {
            @Override
            public String getValue(ImportFileDto object) {
                return String.valueOf(object.getNumItems());
            }
        };
        numItemsCol.setSortable(true);
        
        uploadFailuresCol = new TextColumn<ImportFileDto>() {
            @Override
            public String getValue(ImportFileDto object) {
                return object.getNumFailedUploads().toString();
            }
        };
        uploadFailuresCol.setSortable(true);

        DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        uploadStartDateCol = new Column<ImportFileDto, Date>(dateCell) {
	        @Override
	        public Date getValue(ImportFileDto object) {
	            return object.getUploadStart();
	        }
	    };
	    uploadStartDateCol.setSortable(true);
	    uploadStartDateCol.setDefaultSortAscending(false);

	    uploadEndDateCol = new Column<ImportFileDto, Date>(dateCell) {
	        @Override
	        public Date getValue(ImportFileDto object) {
	            return object.getUploadEnd();
	        }
	    };
	    uploadEndDateCol.setSortable(true);

        lastImportedByCol = new TextColumn<ImportFileDto>() {
            @Override
            public String getValue(ImportFileDto object) {
                if (object.getLastImportUsername() == null) {
                    return "";
                } else {
                    return object.getLastImportUsername();
                }
            }
        };
        lastImportedByCol.setSortable(true);
        
        ActionCell<ImportFileDto> viewDetailCell = new ActionCell<ImportFileDto>(MessagesUtil.getInstance().getMessage("import.upload.open.label"), 
                                                               new ActionCell.Delegate<ImportFileDto>() {
          @Override
          public void execute(ImportFileDto importFileDto) {
              checkDirtyDataParamsAndContinue("openItems", importFileDto);
          }
        });
        
        Column<ImportFileDto, ImportFileDto> viewDetailCol = new Column<ImportFileDto,ImportFileDto>(viewDetailCell) {
            @Override
            public ImportFileDto getValue(ImportFileDto object) {
                return object;
            }
            @Override
            public void render(Context context, ImportFileDto importFileDto, SafeHtmlBuilder sb){
                if (importFileDto.getUploadEnd() == null) {
                    //just don't render the button
                } else {    
                  super.render(context, importFileDto, sb);
               }
            }
            @Override
            public void onBrowserEvent(com.google.gwt.cell.client.Cell.Context context,
                    Element parent, ImportFileDto object, NativeEvent event) {
                if (object.getUploadEnd() != null)  
                    super.onBrowserEvent( context, parent,  object,  event);
            }
        };
        
        ActionCell<ImportFileDto> viewParamsCell = new ActionCell<ImportFileDto>(MessagesUtil.getInstance().getMessage("import.upload.view.params.label"), 
                                                               new ActionCell.Delegate<ImportFileDto>() {
          @Override
          public void execute(ImportFileDto importFileDto) {
              checkDirtyDataParamsAndContinue("openParam", importFileDto);
          }
        });
        
        Column<ImportFileDto, ImportFileDto> viewParamsCol = new Column<ImportFileDto,ImportFileDto>(viewParamsCell) {
            @Override
            public ImportFileDto getValue(ImportFileDto object) {
                return object;
            }
            @Override
            public void render(Context context, ImportFileDto importFileDto, SafeHtmlBuilder sb){
                if (!importFileDto.isHasActionParams()) {
                    //if this filetype doesn't have action params, just don't render the button
                } else {
                    super.render(context, importFileDto, sb);
                }
               
            }
            @Override
            public void onBrowserEvent(com.google.gwt.cell.client.Cell.Context context,
                    Element parent, ImportFileDto object, NativeEvent event) {
                if (object.isHasActionParams())  
                    super.onBrowserEvent( context, parent,  object,  event);
            }
        };
        
        table.addColumn(filenameCol, MessagesUtil.getInstance().getMessage("import.upload.file.name.label"));
        table.addColumn(numItemsCol, MessagesUtil.getInstance().getMessage("import.upload.num.items.label"));
        table.addColumn(uploadFailuresCol, MessagesUtil.getInstance().getMessage("import.upload.num.failed.upload.label"));
        table.addColumn(uploadStartDateCol, MessagesUtil.getInstance().getMessage("import.upload.startdate.label"));
        table.addColumn(uploadEndDateCol, MessagesUtil.getInstance().getMessage("import.upload.enddate.label"));
        table.addColumn(lastImportedByCol, MessagesUtil.getInstance().getMessage("import.upload.last.imported.by.label"));
        table.addColumn(viewDetailCol, MessagesUtil.getInstance().getMessage("import.upload.detail"));
        table.addColumn(viewParamsCol, MessagesUtil.getInstance().getMessage("import.upload.view.params.label"));
        
        // Set the range to display
        table.setVisibleRange(0, getPageSize());

        // Set the data provider for the table
        dataProvider = new AsyncDataProvider<ImportFileDto>() {
            @Override
            protected void onRangeChanged(HasData<ImportFileDto> display) {
                //maintain current sort arrangement, so pick the last ColumnSortInfo off the top of the sortList
                ColumnSortList sortList = table.getColumnSortList();
                int columnIndex = table.getColumnIndex(uploadStartDateCol);
                boolean isAscending = false;
                
                if (sortList != null && sortList.size() != 0) {
                    @SuppressWarnings("unchecked")
                    Column<ImportFileDto, ?> sColumn = (Column<ImportFileDto, ?>) sortList.get(0).getColumn();
                    columnIndex = table.getColumnIndex(sColumn);
                    isAscending = sortList.get(0).isAscending();
                }
                
                if(totalResults != null) {                     //can only change range, if actually HAVE something in table!
                    getTableData(display.getVisibleRange().getStart(), columnIndex, isAscending );
                }
            }
        };
        dataProvider.addDataDisplay(table);

        // Create the table's pager
        pager.setDisplay(table);

        // Set the table's column sorter handler
        columnSortHandler = new AsyncHandler(table) {
            @Override
            public void onColumnSort(ColumnSortEvent event) {
                @SuppressWarnings("unchecked")
                int sortIndex = table.getColumnIndex((Column<ImportFileDto, ?>) event.getColumn());
                boolean isAscending = event.isSortAscending();
                
                getTableData(0, sortIndex, isAscending);
            }
        };
        
        table.addColumnSortHandler(columnSortHandler);
        table.getColumnSortList().push(filenameCol); 
        table.getColumnSortList().push(numItemsCol);
        table.getColumnSortList().push(uploadEndDateCol);
        table.getColumnSortList().push(lastImportedByCol);
        table.getColumnSortList().push(uploadStartDateCol);  //last here so first time it sorts this (top of stack!)

        table.addCellPreviewHandler(new Handler<ImportFileDto>() {
            @Override
            public void onCellPreview(CellPreviewEvent<ImportFileDto> event) {
                if ("mouseover".equals(event.getNativeEvent().getType())) {
                    ImportFileDto cellElement = event.getValue();
                    if (cellElement.getUploadEnd() == null) {
                        table.getRowElement(event.getIndex() - table.getVisibleRange().getStart()).getCells().getItem(event.getColumn())
                        .setTitle(MessagesUtil.getInstance().getMessage("import.file.view.upload.still.busy"));
                    } else {
                        table.getRowElement(event.getIndex() - table.getVisibleRange().getStart()).getCells().getItem(event.getColumn())
                        .setTitle("");
                    }
                }    
            }
        });

        dataProvider.updateRowCount(0, true);
        table.setRowCount(0, true);
        
        clearFilter();
    }
	
	private void checkDirtyDataParamsAndContinue(final String next, final ImportFileDto importFileDto) {
	    if (paramsPanel.isVisible()) {
	        if (((ActionParamsPanel)paramsPanel.getWidget(0)).checkDirtyData()) {
	            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
	                @Override
	                public void confirmed(boolean confirm) {
	                    if (confirm) {
	                        gotoNext(next, importFileDto);
	                    }
	                }
	            });
	        } else {
	            gotoNext(next, importFileDto);
	        }
	    } else {
	        gotoNext(next, importFileDto);
	    }
	}
	private void gotoNext(final String next, final ImportFileDto importFileDto) {
	    if (next.equals("openParam")) {
	        boolean canSubmit = (importFileDto.getLastImportUsername() == null 
                                 && importFileDto.getLastImportStart() == null 
                                 && importFileDto.getLastImportEnd() == null)
	                || (importFileDto.getImportFileTypeClass().equals(MeterMngStatics.FILETYPE_BULK_PRICING_STRUCTURE_CHANGE_GENERATOR)
	                    && importFileDto.getNumSuccessfulImports().equals(0L));
            String message = null;
            if (!canSubmit) {
                message = MessagesUtil.getInstance().getMessage("import.upload.cannot.change.action.params.now");
            }
            if (importFileDto.isHasActionParams()) {
                String importFileName = importFileDto.getImportFilename();                          
                boolean inputFileUploaded = importFileName.startsWith("dummy") ? false : true;             
                loadActionParams(importFileDto.getImportFileTypeClass(), importFileName, message, canSubmit, inputFileUploaded);
            }
	    } else {   //next.equals("openItems")
            parentWorkspace.goToImportFileItemView(importFileDto);
	    }
	}
	
	
    private String getColumnName(int index) {
        // returns fieldName from database passed through to "order by" clause on server
        // side
        switch (index) {
        case 0:
            return "import_filename";
        case 1:
            return "num_items";
        case 2:
            return "upload_failures";
        case 3:
            return "upload_start";
        case 4:
            return "upload_end";
        case 5:
            return "last_import_username";
        default:
            return null;
        }
    }
	
	private void loadTable() {
	    clearTable();
	    clearFilter();

	    final int start = 0;
	    uploadStartDateCol.setDefaultSortAscending(false);   //want descending date order
	    table.getColumnSortList().push(uploadStartDateCol);
	    
	    int uploadStartDateColIndx = table.getColumnIndex(uploadStartDateCol);
	    table.getColumnSortList().push(uploadStartDateCol);
	    getTableData(start, uploadStartDateColIndx, false);
	}
	
	private void refreshTable() {
	    Range range = table.getVisibleRange();
        table.setVisibleRangeAndClearData(range, true);
	}
    
    private void getTableData(final int start, int tableColIndex, Boolean isAscending) {
        clearTable();
        String sortColumnName = getColumnName(tableColIndex);
        
        final int pageSize = getPageSize();
        String order = "ASC";
        if (!isAscending) {
            order = "DESC";
        }
        
        clientFactory.getImportFileDataRpc().selectImportFiles(start, pageSize, sortColumnName, filterColumnName, filterString, filterDate, order, clientFactory.isEnableAccessGroups() ,new ClientCallback<ImportFileListDto>() {
            @Override
            public void onSuccess(ImportFileListDto resultDto) {
                pager.setPageStart(start);

                List<ImportFileDto> result = resultDto.getListImportFileDto();
                //Set the actual total search results count ... done this way because will only send back the results from start so can't use .size()
                totalResults = resultDto.getResultCount();
                dataProvider.updateRowCount(totalResults, true);
                table.setRowCount(totalResults, true);

                //Display the results
                dataProvider.updateRowData(start, result);
                logger.info("Set dataProvider data: start:" + start + " size:" + result.size());
            }
        });
    }
	
	public void clearTable() {
	    dataProvider.updateRowData(0, new ArrayList<ImportFileDto>());  
	    dataProvider.updateRowCount(0, true);
	    totalResults = null;
	    table.setRowCount(0, true);

	    logger.info("clearTable(): Set dataProvider data: start:0  new ArrayList<ImportFileDto>()");
	}
	
	//*******************************************************************************************************
	private void clearFilter() {
	    filterColumnName = null; 
	    filterString = null;
	    filterDate = null;

	    filterDropdown.setSelectedIndex(0);
	    txtbxfilter.setText("");
	    txtbxfilter.setVisible(true);
	    filterDatebox.getTextBox().setText("");
	    filterDatebox.setVisible(false);
	}

	@UiHandler("txtbxfilter")
	void handleFilterChange(KeyUpEvent event) {
	    if (filterDropdown.getSelectedIndex() < 1) {
	        filterString = null;
	        filterTableColIndx = table.getColumnIndex(uploadStartDateCol);
	    } else {
	        String textFilter = txtbxfilter.getText().trim();
	        filterString = textFilter.isEmpty() ? null : textFilter;
	    }
	    changeFilter(filterTableColIndx);
	}

	@UiHandler("filterDropdown")
	void handleFilterDropdownSelection(ChangeEvent changeEvent) {
	    filterColumnName = null; 
	    filterString = null;
	    filterDate = null;

	    txtbxfilter.setText("");
	    filterDatebox.getTextBox().setText("");
        filterDatebox.setVisible(filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.startdate.label")));
        txtbxfilter.setVisible(!filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.startdate.label")));

	    //If have cleared the filter dropdown, leave filterColumnName null and reset the filtering
	    if (filterDropdown.getSelectedIndex() < 1) {
	        filterTableColIndx = table.getColumnIndex(uploadStartDateCol);
	        changeFilter(table.getColumnIndex(uploadStartDateCol));
	        return;
	    }

	    //establish WHICH column to filter on; using db field names to pass to group select
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.file.name.label"))) {
            filterTableColIndx = table.getColumnIndex(filenameCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.startdate.label"))) {
            filterTableColIndx = table.getColumnIndex(uploadStartDateCol);
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("import.upload.last.imported.by.label"))) {
            filterTableColIndx = table.getColumnIndex(lastImportedByCol);
        } else {
            return;
        }

	    //Only set filterColumnName if there WAS actually a selection. 
	    filterColumnName=getColumnName(filterTableColIndx);
	}

	@UiHandler("filterDatebox")
	void handleDateFilterChange(ValueChangeEvent<Date> event) {
	    handleDateFilter();
	}

	protected void handleDateFilter() {
	    if (filterDatebox.getTextBox().getText().trim().isEmpty()) {
	        clearFilter();
	        changeFilter(0);
	    } else {
	        filterDate = filterDatebox.getValue();
	        changeFilter(table.getColumnIndex(uploadStartDateCol));
	    }
	}

	private void changeFilter(int tableColIndx) {
	    final int start = 0;      //table.getVisibleRange().getStart();
	    Integer colIndxInSortList = findColIndxInSortList(tableColIndx);

	    if (colIndxInSortList != null) {
	        ColumnSortInfo colSortInfo = table.getColumnSortList().get(colIndxInSortList);
	        table.getColumnSortList().push(colSortInfo);
	    } else {
	        table.getColumnSortList().push(table.getColumn(tableColIndx));
	    }

	    getTableData(start, tableColIndx, table.getColumnSortList().get(0).isAscending());
	}
    
    private Integer findColIndxInSortList(int tableColIndx) {
        for (int i=0; i<table.getColumnSortList().size(); i++) {
            @SuppressWarnings("unchecked")
            Column<ImportFileDto, ?> sColumn = (Column<ImportFileDto, ?>) table.getColumnSortList().get(i).getColumn();
            if (table.getColumnIndex(sColumn) == tableColIndx) {
                return i;
            }
        }
        return null;
    }

    @Override
    public void displaySelected(ImportFileDto selected) {
    }

	//*******************************************************************************************************

}
