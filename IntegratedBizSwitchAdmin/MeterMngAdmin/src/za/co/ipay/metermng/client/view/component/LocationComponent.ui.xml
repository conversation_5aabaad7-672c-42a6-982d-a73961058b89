<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form" xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:c="urn:import:za.co.ipay.metermng.client.view.component">
	<ui:style>

</ui:style>

<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

<g:FlowPanel>
    <g:HorizontalPanel>
    	<c:LocationGroupComponent ui:field="locationGroupComponent"/>
        <g:FlowPanel ui:field="groupPanel"/>
        <g:FlowPanel>
            <ipay:FormRowPanel ui:field="erfNumberStreetNumberPanel"> 
                <ipay:FormElement debugId="erfNumberElement" ui:field="erfnumberElement" helpMsg="{msg.getLocationErfNumberHelp}" labelText="{msg.getLocationErfNumber}:">
                    <g:TextBox debugId="erfNumberBox" styleName="gwt-TextBox" ui:field="txtbxErfNumber" visibleLength="15" />
                </ipay:FormElement>
                <ipay:FormElement debugId="streetNumberElement" ui:field="streetnumberElement" helpMsg="{msg.getLocationStreetNumberHelp}" labelText="{msg.getLocationStreetNumber}:">
                    <g:TextBox debugId="streetNumberBox" styleName="gwt-TextBox" ui:field="txtbxStreetNumber" visibleLength="15" />
                </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormRowPanel ui:field="buildingNameSuiteNumberPanel">
	            <ipay:FormElement debugId="buildingNameElement" ui:field="buildingNameElement" helpMsg="{msg.getLocationBuildingNameHelp}" labelText="{msg.getLocationBuildingName}:">
	                    <g:TextBox debugId="buildingNameBox" styleName="gwt-TextBox" ui:field="txtbxBuildingName" visibleLength="15" />
	            </ipay:FormElement>
	            <ipay:FormElement debugId="suiteNumberElement" ui:field="suiteNumberElement" helpMsg="{msg.getLocationSuiteNumberHelp}" labelText="{msg.getLocationSuiteNumberName}:">
	                    <g:TextBox debugId="suiteNumberBox" styleName="gwt-TextBox" ui:field="txtbxSuiteNumber" visibleLength="15" />
	            </ipay:FormElement>
            </ipay:FormRowPanel>
            <ipay:FormGroupPanel ui:field="addressPanel">
				<ipay:FormElement debugId="addressElement" ui:field="addressElement" helpMsg="{msg.getLocationAddressHelp}" labelText="{msg.getLocationAddress}:">
					<g:HorizontalPanel spacing="3" ui:field="address1Panel">
					    <g:Cell verticalAlignment="ALIGN_MIDDLE">
					    	<g:HorizontalPanel>
							    <g:Label text="{msg.getLocationFieldAddressLine1} "/>
							    <g:Label ui:field="address1RequiredLabel" visible="false" styleName="formElement_requiredLabel" text="*" />
						    </g:HorizontalPanel>
		                </g:Cell>
		                <g:Cell verticalAlignment="ALIGN_MIDDLE">
		                	<g:VerticalPanel>
							    <g:TextBox debugId="addressBox1" styleName="gwt-TextBox gap" ui:field="txtbxAddress1" visibleLength="35" />
							    <g:Label ui:field="address1ErrorLabel" visible="false" styleName="formElement_errorLabel"/>
						    </g:VerticalPanel>
		                </g:Cell>	
	                </g:HorizontalPanel>
	                <g:HorizontalPanel spacing="3" ui:field="address2Panel">
					    <g:Cell verticalAlignment="ALIGN_MIDDLE">
					    	<g:HorizontalPanel>
						    	<g:Label text="{msg.getLocationFieldAddressLine2} "/>
							    <g:Label ui:field="address2RequiredLabel" visible="false" styleName="formElement_requiredLabel" text="*" />
						    </g:HorizontalPanel>
		                </g:Cell>
		                <g:Cell verticalAlignment="ALIGN_MIDDLE">
		                	<g:VerticalPanel>
						    	<g:TextBox debugId="addressBox2" styleName="gwt-TextBox gap" ui:field="txtbxAddress2" visibleLength="35" />
							    <g:Label ui:field="address2ErrorLabel" visible="false" styleName="formElement_errorLabel"/>
						    </g:VerticalPanel>
		                </g:Cell>	
	                </g:HorizontalPanel>
	                <g:HorizontalPanel spacing="3" ui:field="address3Panel">
					    <g:Cell verticalAlignment="ALIGN_MIDDLE">
					    	<g:HorizontalPanel>
						    	<g:Label text="{msg.getLocationFieldAddressLine3} "/>
							    <g:Label ui:field="address3RequiredLabel" visible="false" styleName="formElement_requiredLabel" text="*" />
						    </g:HorizontalPanel>
		                </g:Cell>
		                <g:Cell verticalAlignment="ALIGN_MIDDLE">
		                	<g:VerticalPanel>
						    	<g:TextBox debugId="addressBox3" styleName="gwt-TextBox gap" ui:field="txtbxAddress3" visibleLength="35" />
							    <g:Label ui:field="address3ErrorLabel" visible="false" styleName="formElement_errorLabel"/>
						    </g:VerticalPanel>
		                </g:Cell>	
	                </g:HorizontalPanel>
				</ipay:FormElement>
            </ipay:FormGroupPanel>
            <ipay:FormRowPanel ui:field="latitudeLongitudePanel">
                <ipay:FormElement debugId="latitudeElement" ui:field="latitudeElement" helpMsg="{msg.getLocationLatHelp}" labelText="{msg.getLocationLat}:">
                    <p2:GPSCoordinateBox debugId="latitudeBox" styleName="gwt-TextBox" ui:field="txtbxLatitude" visibleLength="10" />
                </ipay:FormElement>
                <ipay:FormElement debugId="longitudeElement" ui:field="longitudeElement" helpMsg="{msg.getLocationLongHelp}" labelText="{msg.getLocationLong}:">
                    <p2:GPSCoordinateBox debugId="longitudeBox" styleName="gwt-TextBox" ui:field="txtbxLongitude" visibleLength="10" />
                </ipay:FormElement>
            </ipay:FormRowPanel>
	    </g:FlowPanel>
    </g:HorizontalPanel>
</g:FlowPanel>

</ui:UiBinder> 