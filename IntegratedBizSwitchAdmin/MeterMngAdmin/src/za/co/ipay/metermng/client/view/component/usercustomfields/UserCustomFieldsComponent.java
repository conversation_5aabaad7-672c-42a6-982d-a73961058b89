package za.co.ipay.metermng.client.view.component.usercustomfields;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.appsettings.CustomFieldDto;
import za.co.ipay.metermng.shared.dto.CustomerData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class UserCustomFieldsComponent extends BaseComponent {
    /*
     * These are extra user-defined fields, they are added to the UI depending on the settings in AppSetting
     * NOTE: isVisible() used in this class is custom defined in the inner class CustomFieldDto
     *
     */

    private static Logger logger = Logger.getLogger(UserCustomFieldsComponent.class.getName());
    private static UserCustomFieldsComponentUiBinder uiBinder = GWT.create(UserCustomFieldsComponentUiBinder.class);

    interface UserCustomFieldsComponentUiBinder extends UiBinder<Widget, UserCustomFieldsComponent> {
    }

    @UiField FormGroupPanel userCustomFieldsPanel;

    @UiField FormRowPanel customCharField1Panel;
    @UiField FormElement customCharField1Element;
    @UiField TextBox customCharField1;
    @UiField FormRowPanel customCharField2Panel;
    @UiField FormElement customCharField2Element;
    @UiField TextBox customCharField2;

    @UiField FormRowPanel customNumPanel;
    @UiField FormElement customNumField1Element;
    @UiField BigDecimalValueBox customNumField1;
    @UiField FormElement customNumField2Element;
    @UiField BigDecimalValueBox customNumField2;

    @UiField FormRowPanel customDatePanel;
    @UiField FormElement customDateField1Element;
    @UiField DateBox customDateField1;
    @UiField FormElement customDateField2Element;
    @UiField DateBox customDateField2;

    private ContainsUserCustomFieldsComponent parentWorkspace;
    private String componentType;
    int startVarNameIndx = 0;
    private Map<String, CustomFieldDto> customFieldsRequiredMap;
    protected HasDirtyData hasDirtyData;

    final RegExp usagePointGroupPattern = RegExp.compile("^usagepointgroup\\.[a-zA-Z_]+\\d{1,}\\.(?:status|label|datatype)$");

    public UserCustomFieldsComponent(ClientFactory clientFactory, ContainsUserCustomFieldsComponent parentWorkspace, HasDirtyData hasDirtyData) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.hasDirtyData = hasDirtyData;
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    public boolean configureCustomFields(List<AppSetting> customFieldList, String componentType) {
        this.componentType = componentType;
        if (componentType.equals("usagepoint")) {
            //eg. usagepoint.custom_varchar1.status
            startVarNameIndx = 18;
        } else if (componentType.equals("customer")) {
            //eg. customer.custom_varchar1.status
            startVarNameIndx = 16;
        } else if(componentType.equals("meter")){
            //eg. meter.custom_varchar1.status
            startVarNameIndx = 13;
        }
        logger.info("configureCustomFields: componentType= " + componentType + " customFieldList.size=" + customFieldList.size() + " startVarNameIndx=" + startVarNameIndx);

        userCustomFieldsPanel.setVisible(true);
        //make default map
        setupDefaultCustomStatus();

        return populateCustomFieldSettings(customFieldList);
    }

    private boolean populateCustomFieldSettings(List<AppSetting> customFieldList) {
        boolean hasCustomField = false;

        for (AppSetting as : customFieldList) {
            //eg. usagepoint.custom_varchar1.status
            String appSettingName = as.getKey().toLowerCase();
            if (!appSettingName.contains(componentType.toLowerCase()) || usagePointGroupPattern.test(appSettingName)) {
                continue;
            }

            String key = appSettingName.substring(startVarNameIndx, appSettingName.indexOf(".", startVarNameIndx));
            CustomFieldDto cfd = customFieldsRequiredMap.get(key);

            if (cfd == null) {
               logger.info("populateCustomFieldSettings: key is not catered for in setupDefaultCustomStatus: key= " + key);
               Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("user.custom.fields.error.unknown.setting", new String[] {key}),
                       MediaResourceUtil.getInstance().getErrorIcon(),
                       MessagesUtil.getInstance().getMessage("button.close"));
               continue;
            }

            if (as.getKey().contains("status")) {
                cfd.setStatus(as.getValue().toLowerCase());
                cfd.setStatusId(as.getId());
                customFieldsRequiredMap.put(key, cfd);
            }

            if (as.getKey().contains("label")) {
                cfd.setLabel(as.getValue());
                customFieldsRequiredMap.put(key, cfd);
            }
        }

        //run through whole list removing those that are still unavailable
        for (CustomFieldDto cfd : customFieldsRequiredMap.values()) {
            if (cfd.getName().equals("varchar1")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customCharField1.setValue("");
                    customCharField1Panel.setVisible(false);
                } else {
                    hasCustomField = true;
                    customCharField1Panel.setVisible(true);
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customCharField1Element.setLabelText(cfd.getLabel());
                    } else {
                        customCharField1Element.setLabelText("User Character Field1");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customCharField1Element.setRequired(true);
                    } else {  //OPTIONAL
                        customCharField1Element.setRequired(false);
                    }
                }
            }

            if (cfd.getName().equals("varchar2")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customCharField2.setValue("");
                    customCharField2Panel.setVisible(false);
                } else {
                    hasCustomField = true;
                    customCharField2Panel.setVisible(true);
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customCharField2Element.setLabelText(cfd.getLabel());
                    } else {
                        customCharField2Element.setLabelText("User Character Field2");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customCharField2Element.setRequired(true);
                    } else {  //OPTIONAL
                        customCharField2Element.setRequired(false);
                    }
                }
            }

            if (cfd.getName().equals("numeric1")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customNumField1.setText("");
                    customNumField1Element.setVisible(false);
                } else {
                    hasCustomField = true;
                    customNumField1Element.setVisible(true);
                    customNumPanel.setVisible(true);
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customNumField1Element.setLabelText(cfd.getLabel());
                    } else {
                        customNumField1Element.setLabelText("User Numeric Field1");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customNumField1Element.setRequired(true);
                    } else {  //OPTIONAL
                        customNumField1Element.setRequired(false);
                    }
                }
            }

            if (cfd.getName().equals("numeric2")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customNumField2.setText("");
                    customNumField2Element.setVisible(false);
                } else {
                    hasCustomField = true;
                    customNumField2Element.setVisible(true);
                    customNumPanel.setVisible(true);
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customNumField2Element.setLabelText(cfd.getLabel());
                    } else {
                        customNumField2Element.setLabelText("User Numeric Field2");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customNumField2Element.setRequired(true);
                    } else {  //OPTIONAL
                        customNumField2Element.setRequired(false);
                    }
                }
            }

            if (customNumField1Element.getElement().getStyle().getDisplay().toLowerCase().equals("none")
                    && customNumField2Element.getElement().getStyle().getDisplay().toLowerCase().equals("none")) {
                customNumPanel.setVisible(false);
            }

            if (cfd.getName().equals("timestamp1")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customDateField1.setValue(null);
                    customDateField1Element.setVisible(false);
                } else {
                    hasCustomField = true;
                    customDateField1Element.setVisible(true);
                    customDatePanel.setVisible(true);
                    customDateField1.setFormat(new StrictDateFormat(
                            DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customDateField1Element.setLabelText(cfd.getLabel());
                    } else {
                        customDateField1Element.setLabelText("User Date Field1");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customDateField1Element.setRequired(true);
                    } else {  //OPTIONAL
                        customDateField1Element.setRequired(false);
                    }
                }
            }

            if (cfd.getName().equals("timestamp2")) {
                if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase())
                        || cfd.getStatus().equals("unavailable")) {
                    customDateField2.setValue(null);
                    customDateField2Element.setVisible(false);
                } else {
                    hasCustomField = true;
                    customDateField2Element.setVisible(true);
                    customDatePanel.setVisible(true);
                    customDateField2.setFormat(new StrictDateFormat(
                            DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
                    cfd.setVisible(true);
                    if (cfd.getLabel() != null && !cfd.getLabel().isEmpty()) {
                        customDateField2Element.setLabelText(cfd.getLabel());
                    } else {
                        customDateField2Element.setLabelText("User Date Field2");
                    }
                    if (cfd.getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                            || cfd.getStatus().equals("required")) {
                        customDateField2Element.setRequired(true);
                    } else {  //OPTIONAL
                        customDateField2Element.setRequired(false);
                    }
                }
            }

            if (customDateField1Element.getElement().getStyle().getDisplay().toLowerCase().equals("none")
                    && customDateField2Element.getElement().getStyle().getDisplay().toLowerCase().equals("none")) {
                customDatePanel.setVisible(false);
            }
        }
        return hasCustomField;
    }

    private void setupDefaultCustomStatus() {
        customFieldsRequiredMap = new HashMap<String, CustomFieldDto>();
        addFieldToMap("varchar1");
        addFieldToMap("varchar2");
        addFieldToMap("numeric1");
        addFieldToMap("numeric2");
        addFieldToMap("timestamp1");
        addFieldToMap("timestamp2");
    }

    private void addFieldToMap(String fieldName) {
        customFieldsRequiredMap.put(fieldName, new CustomFieldDto(fieldName, MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable").toLowerCase()));
    }

    public boolean validateCustomFields() {
        boolean validated = true;
        if (customFieldsRequiredMap.get("varchar1").isVisible()) {
            if (customFieldsRequiredMap.get("varchar1").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("varchar1").getStatus().equals("required")) {
                if (customCharField1.getValue() == null || customCharField1.getValue().isEmpty()) {
                    customCharField1Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
            if (validated && customCharField1.getValue() != null && customCharField1.getValue().length() > 255) {
                customCharField1Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.customvarchar.max", new String[]{"255"}));
                validated = false;
            }
        }

        if (customFieldsRequiredMap.get("varchar2").isVisible()) {
            if (customFieldsRequiredMap.get("varchar2").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("varchar2").getStatus().equals("required")) {
                if (customCharField2.getValue() == null || customCharField2.getValue().isEmpty()) {
                    customCharField2Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
            if (validated && customCharField2.getValue() != null && customCharField2.getValue().length() > 255) {
                customCharField2Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.customvarchar.max", new String[]{"255"}));
                validated = false;
            }
        }

        if (customFieldsRequiredMap.get("numeric1").isVisible()) {
            if (customFieldsRequiredMap.get("numeric1").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("numeric1").getStatus().equals("required")) {
                if (customNumField1.getValue() == null) {
                    customNumField1Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
        }

        if (customFieldsRequiredMap.get("numeric2").isVisible()) {
            if (customFieldsRequiredMap.get("numeric2").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("numeric2").getStatus().equals("required")) {
                if (customNumField2.getValue() == null) {
                    customNumField2Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
        }

        if (customFieldsRequiredMap.get("timestamp1").isVisible()) {
            if (customFieldsRequiredMap.get("timestamp1").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("timestamp1").getStatus().equals("required")) {
                if (customDateField1.getValue() == null) {
                    customDateField1Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
            if (customDateField1.getValue() == null && !customDateField1.getTextBox().getValue().isEmpty()) {
                customDateField1Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.date.field.invalid",
                        new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
                validated = false;
            }
        }

        if (customFieldsRequiredMap.get("timestamp2").isVisible()) {
            if (customFieldsRequiredMap.get("timestamp2").getStatus().equals(MessagesUtil.getInstance().getMessage("user.custom.field.status.required").toLowerCase())
                    || customFieldsRequiredMap.get("timestamp2").getStatus().equals("required")) {
                if (customDateField2.getValue() == null) {
                    customDateField2Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
                    validated = false;
                }
            }
            if (customDateField2.getValue() == null && !customDateField2.getTextBox().getValue().isEmpty()) {
                customDateField2Element.showErrorMsg(MessagesUtil.getInstance().getMessage("error.date.field.invalid",
                        new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
                validated = false;
            }
        }

        return validated;
    }

    public void clearCustomFields() {
        customCharField1.setValue("");
        customCharField2.setValue("");

        customNumField1.setText("");
        customNumField2.setText("");

        customDateField1.setValue(null);
        customDateField2.setValue(null);
    }

    public void clearErrorMessages() {
        customCharField1Element.clearErrorMsg();
        customCharField2Element.clearErrorMsg();

        customNumField1Element.clearErrorMsg();
        customNumField2Element.clearErrorMsg();

        customDateField1Element.clearErrorMsg();
        customDateField2Element.clearErrorMsg();
    }

    public void mapDataToForm(String varchar1, String varchar2,
                       BigDecimal numeric1, BigDecimal numeric2,
                       Date timestamp1, Date timestamp2) {
        //if (varchar1 != null && customFieldsRequiredMap.get("varchar1").isVisible()) {
        if (varchar1 != null) {
            customCharField1.setValue(varchar1);
        }
        if (varchar2 != null) {
            customCharField2.setValue(varchar2);
        }

        if (numeric1 != null) {
            customNumField1.setValue(numeric1);
        }
        if (numeric2 != null) {
            customNumField2.setValue(numeric2);
        }

        if (timestamp1 != null) {
            Format format = FormatUtil.getInstance();
            customDateField1.setValue(format.parseDateTime(format.formatDateTime(timestamp1)));
        }
        if (timestamp2 != null) {
            Format format = FormatUtil.getInstance();
            customDateField2.setValue(format.parseDateTime(format.formatDateTime(timestamp2)));
        }
    }

    public void mapFormToData(UsagePointData usagePointData) {
        if (customFieldsRequiredMap.get("varchar1").isVisible()) {
            usagePointData.setCustomVarchar1(customCharField1.getValue());
        }
        if (customFieldsRequiredMap.get("varchar2").isVisible()) {
            usagePointData.setCustomVarchar2(customCharField2.getValue());
        }

        if (customFieldsRequiredMap.get("numeric1").isVisible()) {
            usagePointData.setCustomNumeric1(customNumField1.getValue());
        }
        if (customFieldsRequiredMap.get("numeric2").isVisible()) {
            usagePointData.setCustomNumeric2(customNumField2.getValue());
        }

        if (customFieldsRequiredMap.get("timestamp1").isVisible()) {
            Date date = customDateField1.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(date);
            usagePointData.setCustomTimestamp1(date);
        }
        if (customFieldsRequiredMap.get("timestamp2").isVisible()) {
            Date date = customDateField2.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(date);
            usagePointData.setCustomTimestamp2(date);
        }
    }

    public void mapFormToData(MeterData meterData) {
        if (customFieldsRequiredMap.get("varchar1").isVisible()) {
            meterData.setCustomVarchar1(customCharField1.getValue());
        }
        if (customFieldsRequiredMap.get("varchar2").isVisible()) {
            meterData.setCustomVarchar2(customCharField2.getValue());
        }

        if (customFieldsRequiredMap.get("numeric1").isVisible()) {
            meterData.setCustomNumeric1(customNumField1.getValue());
        }
        if (customFieldsRequiredMap.get("numeric2").isVisible()) {
            meterData.setCustomNumeric2(customNumField2.getValue());
        }

        if (customFieldsRequiredMap.get("timestamp1").isVisible()) {
            Date date = customDateField1.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(date);
            meterData.setCustomTimestamp1(date);
        }
        if (customFieldsRequiredMap.get("timestamp2").isVisible()) {
            Date date = customDateField2.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(date);
            meterData.setCustomTimestamp2(date);
        }
    }

    public boolean mapFormToData(CustomerData customerData) {
    	boolean isUpdate = false;
        if (customFieldsRequiredMap.get("varchar1").isVisible()) {
        	if ((customCharField1.getValue() != null && !customCharField1.getValue().equals(customerData.getCustomVarchar1()))
        			|| (customerData.getCustomVarchar1() != null && !customerData.getCustomVarchar1().equals(customCharField1.getValue()))) {
        		isUpdate = true;
        	}
            customerData.setCustomVarchar1(customCharField1.getValue());
        }
        if (customFieldsRequiredMap.get("varchar2").isVisible()) {
        	if ((customCharField2.getValue() != null && !customCharField2.getValue().equals(customerData.getCustomVarchar2()))
        			|| (customerData.getCustomVarchar2() != null && !customerData.getCustomVarchar2().equals(customCharField2.getValue()))) {
        		isUpdate = true;
        	}
            customerData.setCustomVarchar2(customCharField2.getValue());
        }

        if (customFieldsRequiredMap.get("numeric1").isVisible()) {
        	if ((customNumField1.getValue() != null && !customNumField1.getValue().equals(customerData.getCustomNumeric1()))
        			|| (customerData.getCustomNumeric1() != null && !customerData.getCustomNumeric1().equals(customNumField1.getValue()))) {
        		isUpdate = true;
        	}
            customerData.setCustomNumeric1(customNumField1.getValue());
        }
        if (customFieldsRequiredMap.get("numeric2").isVisible()) {
        	if ((customNumField2.getValue() != null && !customNumField2.getValue().equals(customerData.getCustomNumeric2()))
        			|| (customerData.getCustomNumeric2() != null && !customerData.getCustomNumeric2().equals(customNumField2.getValue()))) {
        		isUpdate = true;
        	}
            customerData.setCustomNumeric2(customNumField2.getValue());
        }

        if (customFieldsRequiredMap.get("timestamp1").isVisible()) {
            Date customDate = customDateField1.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(customDate);
            Date customTimestamp = customerData.getCustomTimestamp1();
            if ((customDate != null && !customDate.equals(customTimestamp))
                    || (customTimestamp != null && !customTimestamp.equals(customDate))) {
                isUpdate = true;
            }
            customerData.setCustomTimestamp1(customDate);
        }
        if (customFieldsRequiredMap.get("timestamp2").isVisible()) {
            Date customDate = customDateField2.getValue();
            MeterMngClientUtils.removeTimeZoneOffset(customDate);
            Date customTimestamp = customerData.getCustomTimestamp2();
            if ((customDate != null && !customDate.equals(customTimestamp))
                    || (customTimestamp != null && !customTimestamp.equals(customDate))) {
                isUpdate = true;
            }
            customerData.setCustomTimestamp2(customDate);
        }
        return isUpdate;
    }

    public void refreshCustomAppSettings() {
        clientFactory.getAppSettingRpc().getAppSettingsForUPAndMeterAndCustomerCustomFields(new ClientCallback<ArrayList<AppSetting>>() {

            @Override
            public void onFailure(Throwable caught) {
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("user.custom.fields.error.get", new String[] {"usage point / customer"}),
                        MediaResourceUtil.getInstance().getLockedIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            }

            @Override
            public void onSuccess(ArrayList<AppSetting> result) {
                if (!populateCustomFieldSettings(result)) {
                    parentWorkspace.removeUserCustomFieldsComponent();
                } else {
                    parentWorkspace.addUserCustomFieldsComponent();
                }
            }
        });
    }

    public Map<String, CustomFieldDto> getCustomFieldsRequiredMap() {
        return customFieldsRequiredMap;
    }

    //SERVICE METHODS ********************************************************************************************************
    public void getCustomFieldsWithHistoryVisibilityMap(ArrayList<AppSetting> customFieldList, String componentType) {

        configureCustomFields(customFieldList, componentType);

        Map<String, CustomFieldDto> customFieldsHistoryRequiredMap = new HashMap<String, CustomFieldDto>(customFieldsRequiredMap);
            clientFactory.getAppSettingRpc().getAppSettingsCustomFieldsHistoryVisibility(customFieldsHistoryRequiredMap,
                                                                                         new ClientCallback<Map<String, CustomFieldDto>>() {

                @Override
                public void onFailure(Throwable caught) {
                    logger.info("UserCustomFieldsComponent FAILED. Throwable = " + caught.toString());
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("user.custom.fields.error.get", new String[] {"historyInfo"}),
                            MediaResourceUtil.getInstance().getLockedIcon(),
                            MessagesUtil.getInstance().getMessage("button.close"));
                }

                @Override
                public void onSuccess(Map<String, CustomFieldDto> result) {
                    parentWorkspace.setHistoryVisibilityMap(result);
                }
            });
    }

    public void addFieldHandlers() {
        customCharField1.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        customCharField2.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        customNumField1.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        customNumField2.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        customDateField1.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        customDateField2.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
    }
}
