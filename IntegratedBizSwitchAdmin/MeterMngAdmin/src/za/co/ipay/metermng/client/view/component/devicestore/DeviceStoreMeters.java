package za.co.ipay.metermng.client.view.component.devicestore;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.AssignMeterDialogueBox;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.MeterComponent;
import za.co.ipay.metermng.client.view.component.RemoveMeterDialogueBox;
import za.co.ipay.metermng.client.view.component.meter.ContainsMeterComponent;
import za.co.ipay.metermng.client.view.component.meter.MeterInformation;
import za.co.ipay.metermng.client.view.workspace.DeviceStoreWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.MeterModelData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class DeviceStoreMeters extends BaseComponent implements IpayDataProviderFilter<MeterData>, ContainsMeterComponent{ 
    
    private Logger logger = Logger.getLogger(DeviceStoreMeters.class.getName());

    private static final int DEFAULT_PAGE_SIZE = 15;
         
    interface DeviceStoreMetersTableUiBinder extends UiBinder<Widget, DeviceStoreMeters> {
    }

    private static DeviceStoreMetersTableUiBinder uiBinder = GWT.create(DeviceStoreMetersTableUiBinder.class);

    @UiField(provided=true) CellTable<MeterData> clltblMeters;
    @UiField TablePager smplpgrMeters; 
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField PageHeader pageHeader;
    @UiField HTML tableTitle;
    @UiField(provided=true) public MeterComponent metercomponent;
    @UiField(provided=true) public MeterInformation meterInfo;
    
    private IPayDataProvider<MeterData> dataProvider;
    private ArrayList<MeterData> themeterdata;
    private SingleSelectionModel<MeterData> selectionModel;
    
    private Column<MeterData, Date> dateManufacturedCol;
    private ListHandler<MeterData> columnSortHandler;
    
    private DeviceStoreWorkspaceView parentWorkspace;
    private EndDeviceStoreData selectedDeviceStore;
    private HasDirtyData hasDirtyData;
    
    public DeviceStoreMeters(DeviceStoreWorkspaceView parentWorkspace, ClientFactory clientFactory, String header, String title, List<AppSetting> customFields) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.hasDirtyData = parentWorkspace.createAndRegisterHasDirtyData();
        metercomponent = new MeterComponent(clientFactory, this, customFields);
        meterInfo = new MeterInformation(clientFactory, null);
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initHeaders(header, title);
        initView();
        initTable();
        meterInfo.setVisible(false);
        updateUserInterfaceComponentSettings();
    }
    
    protected void initHeaders(String header, String title) {
       setHeader(header);
       setTitle(title);
    }
    
    protected void createTable() {
         clltblMeters = new CellTable<MeterData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
         clltblMeters.ensureDebugId("deviceStoreMetersTable");
    }
    
    private void initView() {
        Anchor anchor = new Anchor(MessagesUtil.getInstance().getMessage("devicestores.header"));
        anchor.addClickHandler(new ClickHandler() {            
            @Override
            public void onClick(ClickEvent event) {
                hasDirtyData.checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            hasDirtyData.setDirtyData(false);
                            parentWorkspace.goToDeviceStores();
                        }
                    }
                });
            }
        });
        anchor.ensureDebugId("backAnchor");
        pageHeader.addPageHeaderLink(anchor);
    
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.number"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.serial"));
        dataName.setText(MessagesUtil.getInstance().getMessage("devicestore.meters"));
        dataDescription.setText(MessagesUtil.getInstance().getMessage("devicestore.meters.description"));
        clientFactory.getEventBus().addHandler(EndDeviceStoreUpdatedEvent.TYPE, new EndDeviceStoreUpdatedEventHandler() {
            
            @Override
            public void processEndDeviceStoreUpdatedEvent(EndDeviceStoreUpdatedEvent event) {
                if (event.getEndDeviceStoreId() != null && event.getEndDeviceStoreId().equals(selectedDeviceStore.getId())) {
                    refreshMetersList();
                }
            }
        });
    }
    
    private void initTable() {
        
         if (dataProvider == null) {
             dataProvider = new IPayDataProvider<MeterData>(this);
        
             TextColumn<MeterData> meternumberColumn = new TextColumn<MeterData>() {
                 @Override
                 public String getValue(MeterData data) {
                     return data.getMeterNum();
                 }
             };
             
             TextColumn<MeterData> serialnumberColumn = new TextColumn<MeterData>() {
                 @Override
                 public String getValue(MeterData data) {
                     return data.getSerialNum();
                 }
                 
             };
             
            TextColumn<MeterData> statusColumn = new StatusTableColumn<MeterData>() {
                @Override
                public String getCellStyleNames(Context context, MeterData data) {
                    String style = super.getCellStyleNames(context, data);
                    return style;
                }
            };
            
            TextColumn<MeterData> tokentechcodeColumn = new TextColumn<MeterData>() {
                @Override
                public String getValue(MeterData data) {
                    if (data.getStsMeter() != null) {
                        return data.getStsMeter().getStsTokenTechCode();
                    }
                    return "";
                }
            };
            
            TextColumn<MeterData> algcodeColumn = new TextColumn<MeterData>() {
                @Override
                public String getValue(MeterData data) {
                    if (data.getStsMeter() != null) {
                        return data.getStsMeter().getStsAlgorithmCode();
                    }
                    return "";
                }
            };
            
            TextColumn<MeterData> tariffIndexColumn = new TextColumn<MeterData>() {
                @Override
                public String getValue(MeterData data) {
                    if (data.getStsMeter() != null) {
                        return data.getStsMeter().getStsCurrTariffIndex();
                    }
                    return "";
                }
            };
            
            TextColumn<MeterData> supplyGroupCodeColumn = new TextColumn<MeterData>() {
                @Override
                public String getValue(MeterData data) {
                    if (data.getStsMeter() != null) {
                        return data.getStsMeter().getStsCurrSupplyGroupCode();
                    }
                    return "";
                }
            };
            
            TextColumn<MeterData> keyRevisionColumn = new TextColumn<MeterData>() {
                @Override
                public String getValue(MeterData data) {
                    if (data.getStsMeter() != null) {
                        if (data.getStsMeter().getStsCurrKeyRevisionNum()==null) {
                            return "";
                        } 
                        return String.valueOf(data.getStsMeter().getStsCurrKeyRevisionNum());
                    }
                    return "";
                }
                
            };
            
            clltblMeters.addColumn(meternumberColumn, MessagesUtil.getInstance().getMessage("meter.number.column"));
            clltblMeters.addColumn(serialnumberColumn, MessagesUtil.getInstance().getMessage("meter.serial.column"));
            clltblMeters.addColumn(statusColumn, MessagesUtil.getInstance().getMessage("meter.status.column"));
            clltblMeters.addColumn(tokentechcodeColumn, MessagesUtil.getInstance().getMessage("meter.techtoken.column"));
            clltblMeters.addColumn(algcodeColumn, MessagesUtil.getInstance().getMessage("meter.alg.column"));
            clltblMeters.addColumn(supplyGroupCodeColumn, MessagesUtil.getInstance().getMessage("meter.supplygroup.column"));
            clltblMeters.addColumn(keyRevisionColumn, MessagesUtil.getInstance().getMessage("meter.keyrevision.column"));
            clltblMeters.addColumn(tariffIndexColumn, MessagesUtil.getInstance().getMessage("meter.tariffindex.column"));
           
            dataProvider.addDataDisplay(clltblMeters);
            smplpgrMeters.setDisplay(clltblMeters);
            
            selectionModel = new SingleSelectionModel<MeterData>();
            CellPreviewEvent.Handler<MeterData> handler = new CellPreviewEvent.Handler<MeterData>() {                
                final CellPreviewEvent.Handler<MeterData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();                
                @Override
                public void onCellPreview(final CellPreviewEvent<MeterData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (metercomponent.getHasDirtyData().isDirtyData()) {                                   
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                        @Override
                                        public void confirmed(boolean confirm) {
                                            if (confirm) {
                                                metercomponent.getHasDirtyData().setDirtyData(false);
                                                event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                logger.info("Discarding changes for selection event");
                                            } else {
                                                event.setCanceled(true);
                                                logger.info("Cancelled selection event, staying with current selection");
                                            }
                                            
                                        }});
                                } else {
                                    selectionEventManager.onCellPreview(event);
                                }
                            }
                    } else {
                        selectionEventManager.onCellPreview(event);
                    }
                }};            
            clltblMeters.setSelectionModel(selectionModel, handler);
            selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
                public void onSelectionChange(SelectionChangeEvent event) {
                    MeterData selected = selectionModel.getSelectedObject();
                    if (selected != null) {
                        setMeter(selected); 
                    }
                }
            });
         
             
         }
    }
    
    public void setMetersList(EndDeviceStoreData deviceStoreData) {
        this.setEndDeviceStore(deviceStoreData);
        refreshMetersList();
    }
    
    public void refreshMetersList() {
        clientFactory.getMeterRpc().getMetersByEndDeviceStore(selectedDeviceStore.getId(), new ClientCallback<ArrayList<MeterData>>() {
            @Override
            public void onSuccess(ArrayList<MeterData> result) {
                if (result != null) {
                    themeterdata = result;
                    dataProvider.setList(themeterdata);
                    dataProvider.refresh();
                    
                    columnSortHandler = new ListHandler<MeterData>(dataProvider.getList());
                    
                    columnSortHandler.setComparator(dateManufacturedCol, new Comparator<MeterData>() {
                        public int compare(MeterData o1, MeterData o2) {
                            if (o1 == o2) {
                                return 0;
                            }
                            return -1;
                        }
                    });
                    
                    clltblMeters.addColumnSortHandler(columnSortHandler);
                    clltblMeters.getColumnSortList().push(dateManufacturedCol);
                    clltblMeters.setPageStart(0);
                }
            }
        });   
    }

    @Override
    public boolean isValid(MeterData value, String filter) {
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("meter.number"))) {
            return value.getMeterNum().contains(filter);
        } else {
            return value.getSerialNum().toLowerCase().contains(filter);
        } 
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(dataProvider.getList());
        columnSortHandler.setList(dataProvider.getList());
        smplpgrMeters.firstPage();
        
    }
    
    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
    }
    
    public void setHeader(String header) {
        pageHeader.setHeading(header);
    }
    
    public void setTitle(String title) {
        tableTitle.setHTML(title);
    }
    
    public void setEndDeviceStore(EndDeviceStoreData endDeviceStoreData) {
        this.selectedDeviceStore = endDeviceStoreData;
        pageHeader.setHeading(selectedDeviceStore.getName());
        dataName.setText(MessagesUtil.getInstance().getMessage("devicestore.meters.in")+" "+selectedDeviceStore.getName());
        metercomponent.setEndDeviceStoreId(this.selectedDeviceStore.getId(), this.selectedDeviceStore.getAccessGroupId());
    }

    @Override
    public void onSaveMeter(MeterData meterData, Integer buttonTop, Integer buttonLeft) {
        setMetersList(selectedDeviceStore);
        meterInfo.setVisible(false);
        meterInfo.setMeterInfo(new MeterData(), null);
        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meter.saved"), MediaResourceUtil.getInstance().getInformationIcon(), buttonLeft, buttonTop, null);
    }

    @Override
    public void onCancelMeter() {
        resetMeterFormUI();
    }

    public void resetMeterFormUI() {
        MeterData emptyMeterData = new MeterData();
        metercomponent.setMeterData(emptyMeterData);
        meterInfo.setVisible(false);
        meterInfo.setMeterInfo(emptyMeterData, null);
        MeterData selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    @Override
    public void onAssignMeter(AssignMeterDialogueBox assignMeterDialogueBox) {
    }

    @Override
    public void onRemoveMeter(RemoveMeterDialogueBox removeMeterDialogueBox) {
    }

    @Override
    public UsagePointData getUsagePoint() {
        return null;
    }

    @Override
    public void setMeter(final MeterData meterData) {
        if (meterData != null) {
            if(meterData.getMeterModelData() == null) {
                // lazy load model data
                clientFactory.getMeterModelRpc().getMeterModelById(meterData.getMeterModelId(), new ClientCallback<MeterModelData>() {
                    @Override
                    public void onSuccess(MeterModelData meterModelData) {
                        meterData.setMeterModelData(meterModelData);
                        metercomponent.setMeterData(meterData);
                        meterInfo.setVisible(true);
                        meterInfo.setMeterInfo(meterData, null);
                        // Order is important - first set meterData before populating MDC Trans
                        meterInfo.populateMdcTrans();
                    }
                });
            } else {
                metercomponent.setMeterData(meterData);
                meterInfo.setVisible(true);
                meterInfo.setMeterInfo(meterData, null);
            }
        } else {
            metercomponent.setMeterData(meterData);
        }
    }
    
    @Override
    public Workspace getWorkspace() {
        return parentWorkspace;
    }
    
    public void updateUserInterfaceComponentSettings() {
        clientFactory.getUserInterfaceRpcAsync().getFormFields(new ClientCallback<Map<String, FormFields>>() {
            @Override
            public void onSuccess(Map<String, FormFields> result) {
                metercomponent.updateUserInterfaceComponentSettings(result);
            }
        });
    }
}
    
