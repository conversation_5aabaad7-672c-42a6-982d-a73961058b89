package za.co.ipay.metermng.client.view.component.importfile;

import java.math.BigDecimal;
import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.samras.SamrasDebtImportRecord;

public class SamrasDebtImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private SamrasDebtImportRecord recordIn;

    public SamrasDebtImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        SamrasDebtImportRecord record = recordIn = itemDto.getSamrasDebtImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Account Number", record.getAccountNumber()));
        dataList.add(new ImportRecordField("Arrears Balance", record.getArrearsBalance().toPlainString()));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        SamrasDebtImportRecord chgRec = createRecordFromList();
        isDirtyData = false;
        if (!chgRec.getAccountNumber().equals(recordIn.getAccountNumber())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getArrearsBalance().equals(recordIn.getArrearsBalance())) {
            isDirtyData = true;
            return;
        }
    }

    private SamrasDebtImportRecord createRecordFromList() {
        SamrasDebtImportRecord chgRec = new SamrasDebtImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Account Number")) {
                chgRec.setAccountNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("Arrears Balance")) {
                chgRec.setArrearsBalance(new BigDecimal(field.getFieldValue()));
            }
        }
        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }

    @Override
    protected void displayUpdateMessage() {
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.account.number.update.success",
                        new String[] { createRecordFromList().getAccountNumber() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItem) {
        importFileItem.setGenericImportRecord(createRecordFromList());
    }
}