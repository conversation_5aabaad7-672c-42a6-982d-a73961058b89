package za.co.ipay.metermng.client.view.component.devicestore;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.gwt.common.shared.exception.AccessControlException;
import za.co.ipay.metermng.client.event.EndDeviceStoreUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.DeviceStorePlace;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.DeviceStoreWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.EndDeviceStoreData;
import za.co.ipay.metermng.shared.EndDeviceStoreHistData;

public class DeviceStoreView extends BaseWorkspace implements FormManager<EndDeviceStoreData> {

    private Logger logger = Logger.getLogger(DeviceStoreView.class.getName());

    private DeviceStoreWorkspaceView parentWorkspace;
    private EndDeviceStoreData deviceStore;
    private List<EndDeviceStoreData> deviceStoreList;
    private SimpleTableView<EndDeviceStoreData> view;
    private DeviceStorePanel panel;
    private Button addMetersBtn;
    private ListDataProvider<EndDeviceStoreData> dataProvider = new ListDataProvider<EndDeviceStoreData>();
    private DeviceStoreHistoryView historyView;
    private HorizontalPanel extraButtonsPanel = new HorizontalPanel();

    private Messages messages = MessagesUtil.getInstance();

    public DeviceStoreView(DeviceStoreWorkspaceView parentWorkspace,
                            ClientFactory clientFactory,
                            SimpleTableView<EndDeviceStoreData> view) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        this.view = view;
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);
        historyView = new DeviceStoreHistoryView();
        historyView.ensureDebugId("deviceStoreHistoryView");
        view.getBelowFormPanel().add(historyView);
        view.getBelowFormPanel().setVisible(false);
        extraButtonsPanel.setSpacing(5);
    }

    private void initForm() {
        panel = new  DeviceStorePanel(view.getForm(), clientFactory);
        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("devicestore.title.add"));

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onSaveButtonClick();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("deviceStoreViewSaveButton");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            displaySelected(null);
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("deviceStoreViewCancelButton");

        addMetersBtn = new Button(MessagesUtil.getInstance().getMessage("devicestore.button.addmeters"));
        addMetersBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (view.getForm().isDirtyData()) {
                                view.getForm().setDirtyData(false);
                                setDeviceStore(deviceStore);
                            }
                            parentWorkspace.goToAddMeters(deviceStore);
                        }
                    }
                });
            }
        });
        addMetersBtn.setVisible(false);
        addMetersBtn.ensureDebugId("addMetersButton");
        extraButtonsPanel.add(addMetersBtn);

        view.getForm().getSecondaryButtons().add(extraButtonsPanel);
        view.getForm().getSecondaryButtons().setVisible(true);
    }

    private void createTable() {
        TextColumn<EndDeviceStoreData> nameColumn = new TextColumn<EndDeviceStoreData>() {
            @Override
            public String getValue(EndDeviceStoreData deviceStore) {
                return deviceStore.getName();
            }
        };
        nameColumn.setSortable(true);
        TextColumn<EndDeviceStoreData> descriptionColumn = new TextColumn<EndDeviceStoreData>() {
            @Override
            public String getValue(EndDeviceStoreData deviceStore) {
                return deviceStore.getDescription();
            }
        };

        TextColumn<EndDeviceStoreData> statusColumn = new StatusTableColumn<EndDeviceStoreData>();

        // Add the columns.
        view.getTable().addColumn(nameColumn, MessagesUtil.getInstance().getMessage("devicestore.field.name"));
        view.getTable().addColumn(descriptionColumn, MessagesUtil.getInstance().getMessage("devicestore.field.description"));
        view.getTable().addColumn(statusColumn, MessagesUtil.getInstance().getMessage("devicestore.field.active"));

        view.getTable().ensureDebugId("deviceStoresTable");

        dataProvider.addDataDisplay(view.getTable());
        view.getPager().setDisplay(view.getTable());
        view.getTable().setPageSize(getPageSize());
    }

    private void setDeviceStore(EndDeviceStoreData deviceStore) {
        panel.reset();
        this.deviceStore = deviceStore;
        if (deviceStore != null) {
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("devicestore.title.update"));
            addMetersBtn.setVisible(true);
            clientFactory.getDeviceStoreRpc().getEndDeviceStoreHistory(deviceStore.getId(), new ClientCallback<ArrayList<EndDeviceStoreHistData>>() {

                @Override
                public void onSuccess(ArrayList<EndDeviceStoreHistData> result) {
                    historyView.setDeviceStoreHistoryList(result);
                    view.getBelowFormPanel().setVisible(true);
                }

            });

        } else {
            deviceStore = new EndDeviceStoreData();
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("devicestore.title.add"));
            addMetersBtn.setVisible(false);
            view.clearTableSelection();

            view.getBelowFormPanel().setVisible(false);
        }

        panel.nameTextBox.setText(deviceStore.getName());
        panel.descriptionTextBox.setText(deviceStore.getDescription());
        panel.chckbxActive.setValue(deviceStore.getRecordStatus().equals(RecordStatus.ACT));
        panel.locationComponent.setLocation(deviceStore.getLocation());

        if (deviceStore.isStoresOtherVendorsMeter()) {
            panel.storesOtherVendorsMeter.setValue(deviceStore.isStoresOtherVendorsMeter());
            panel.setModifiedHelpMessage(panel.storesOtherVendorsMeterElement, messages.getMessage("devicestore.field.store_vendors_meter_help"));
            panel.customMsgRow.setVisible(false);
        } else if (deviceStore.getCustomMessage() != null && !deviceStore.getCustomMessage().trim().isEmpty()) {
            panel.customMsg.setText(deviceStore.getCustomMessage().trim());
            panel.storesOtherVendorsMeterRow.setVisible(false);
            panel.setModifiedHelpMessage(panel.customMsgElement, messages.getMessage("devicestore.field.custom_message_help"));
        }

        // For Access Groups confirm edit ability
        view.getForm().getSaveBtn().setVisible(true);
        if (deviceStore.getId() != null && !clientFactory.hasAccessGroupsEditPermissions(deviceStore.getAccessGroupId())) {
            view.getForm().getSaveBtn().setVisible(false);
        }
    }

    public void populate() {
        clientFactory.getDeviceStoreRpc().getDeviceStores(new ClientCallback<List<EndDeviceStoreData>>() {
            @Override
            public void onSuccess(List<EndDeviceStoreData> result) {
                deviceStoreList = result;
                if (!deviceStoreList.isEmpty()) {
                    dataProvider.setList(deviceStoreList);
                    dataProvider.refresh();
                }
            }

            @Override
            public void onFailure(Throwable caught) {
                if (caught instanceof AccessControlException) {
                    clientFactory.getWorkspaceContainer().closeWorkspaceNow(DeviceStorePlace.ALL_DEVICE_STORE_PLACE);
                }
                super.onFailure(caught);
            }
        });
    }

    @Override
    public void onLeaving() {

    }

    private void onSaveButtonClick() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (view.getForm().getSaveBtn().getText().equals(MessagesUtil.getInstance().getMessage("button.create"))){
                    addDeviceStore();
                } else {
                    updateDeviceStore();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void update() {
        if (deviceStore == null) {
            this.deviceStore = new EndDeviceStoreData();
        }
        deviceStore.setName(panel.nameTextBox.getText());
        deviceStore.setDescription(panel.descriptionTextBox.getText());
        if (panel.chckbxActive.getValue()) {
            deviceStore.setRecordStatus(RecordStatus.ACT);
        } else {
            deviceStore.setRecordStatus(RecordStatus.DAC);
        }
        panel.locationComponent.mapFormToData();
        deviceStore.setLocation(panel.locationComponent.getLocation());
        deviceStore.setCustomMessage(panel.customMsg.getText().trim());
        deviceStore.setStoresOtherVendorsMeter(panel.storesOtherVendorsMeter.getValue());
    }

    private boolean isValid() {
        boolean valid = true;

        panel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(deviceStore, "name", panel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(deviceStore, "description", panel.descriptionElement)) {
            valid = false;
        }
        if (!panel.locationComponent.validate()) {
            valid = false;
        }
        return valid;
    }

    private void addDeviceStore() {
        update();
        if (isValid()) {
            clientFactory.getDeviceStoreRpc().addDeviceStore(deviceStore, new ClientCallback<EndDeviceStoreData>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                @Override
                public void onSuccess(EndDeviceStoreData result) {
                    if (result != null) {
                        view.getForm().setDirtyData(false);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("devicestore.title") }),
                                MediaResourceUtil.getInstance().getInformationIcon(),
                                view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        deviceStoreList.add(result);
                        dataProvider.setList(deviceStoreList);
                        dataProvider.refresh();
                        setDeviceStore(null);
                        clientFactory.getEventBus().fireEvent(new EndDeviceStoreUpdatedEvent());
                        populate();
                    }
                }

                @Override
                public void onFailure(Throwable caught) {
                    if (caught instanceof AccessControlException) {
                        clientFactory.getWorkspaceContainer().closeWorkspaceNow(DeviceStorePlace.ALL_DEVICE_STORE_PLACE);
                    }
                    super.onFailure(caught);
                }
            });
        }
    }

    public void updateDeviceStore() {
        update();
        if (isValid()) {
                clientFactory.getDeviceStoreRpc().updateDeviceStore(deviceStore, new ClientCallback<EndDeviceStoreData>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                    @Override
                    public void onSuccess(EndDeviceStoreData result) {
                        view.getForm().setDirtyData(false);
                        if (result != null) {
                            int index = -1;
                            for (int i = 0; i < deviceStoreList.size(); i++) {
                                if (deviceStoreList.get(i).getId().equals(result.getId())){
                                    index = i;
                                    break;
                                }
                            }
                            if (index > -1) {
                                setDeviceStore(null);
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("devicestore.title") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                deviceStoreList.set(index, result);
                                dataProvider.setList(deviceStoreList);
                                dataProvider.refresh();
                                clientFactory.getEventBus().fireEvent(new EndDeviceStoreUpdatedEvent());
                                populate();
                            }
                        }
                    }
                    @Override
                    public void onFailure(Throwable caught) {
                        if (caught instanceof AccessControlException) {
                            clientFactory.getWorkspaceContainer().closeWorkspaceNow(DeviceStorePlace.ALL_DEVICE_STORE_PLACE);
                        }
                        super.onFailure(caught);
                    }
                });
            }
    }

    @Override
    public void onArrival(Place place) {
        logger.info("Arrived at device store");
        populate();
    }

    public void clear() {
        deviceStore = new EndDeviceStoreData();
        panel.clearFields();
        panel.clearErrors();
        addMetersBtn.setVisible(false);
        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("devicestore.title.add"));
        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
    }

    @Override
    public void onClose() {
    }

    @Override
    public boolean handles(Place place) {
        return  place instanceof DeviceStorePlace;
    }

    @Override
    public void onSelect() {

    }

    @Override
    public void displaySelected(EndDeviceStoreData selected) {
        setDeviceStore(selected);
    }

}
