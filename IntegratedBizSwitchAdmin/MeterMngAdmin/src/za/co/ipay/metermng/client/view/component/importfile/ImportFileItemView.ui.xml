<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client" 
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets"
             xmlns:p1="urn:import:za.co.ipay.gwt.common.client.workspace"
             xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:ipay="urn:import:za.co.ipay.metermng.client.view.component.importfile">
    <ui:style>
       .spaceAbove {
            margin-top: 0.9em;
        }
    </ui:style>
    
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
        <g:DockLayoutPanel ui:field="mainPanel" styleName="mainPanel">
 
        <g:north size="30">
            <form:PageHeader heading="" ui:field="pageHeader" />
        </g:north>

        <g:center>
          <g:ScrollPanel styleName="{style.spaceAbove}">
              <g:FlowPanel width="99%" height="100%">
      
                  <ipay:FileDetailPanel ui:field="fileDetailPanel" />
                
                  <g:FlowPanel>
                        <g:HTML ui:field="dataName" text="" styleName="dataTitle"/>
                        <g:HTMLPanel>
            
                            <g:HorizontalPanel spacing="3">
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:Label text="{msg.getMeterTxnFilter}:" styleName="gwt-Label-bold"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:ListBox visibleItemCount="1" name="Filter" ui:field="filterDropdown"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <g:TextBox ui:field="txtbxfilter"/>
                                </g:Cell>
                                <g:Cell verticalAlignment="ALIGN_MIDDLE">
                                    <p2:DateBox ui:field="filterDatebox" visible="false"/>
                                </g:Cell>
                            </g:HorizontalPanel>
                      
                            <g:HTMLPanel ui:field="tablePanel">
                                <g2:CellTable ui:field="table" />
                            </g:HTMLPanel>
                            <widget:TablePager ui:field="pager" styleName="pager" location="CENTER" />
            
                        </g:HTMLPanel>
                        <g:HTMLPanel ui:field="buttons" styleName="mainButtons">
                            <g:Button text="{msg.getImportSelectedButton}" ui:field="importSelectedBtn" />
                            <g:Button text="{msg.getImportAllButton}" ui:field="importAllBtn" />
                            <g:Button text="{msg.getStopImportAllButton}" ui:field="stopImportAllBtn" visible="false"/>
                            <g:Button ui:field="extractBtn" visible="false"/>
                            <g:Button ui:field="spareBtn" visible="false"/>
                            <g:Button text="{msg.getBackButton}" ui:field="backBtn" />
                        </g:HTMLPanel>
              
                  </g:FlowPanel>
                
              </g:FlowPanel>
          </g:ScrollPanel>
        </g:center>
      </g:DockLayoutPanel>
    
</ui:UiBinder> 