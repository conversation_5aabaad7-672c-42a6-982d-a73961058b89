<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component"
    xmlns:ipayusercustomfields="urn:import:za.co.ipay.metermng.client.view.component.usercustomfields"
    xmlns:form="urn:import:za.co.ipay.gwt.common.client.form">

    <ui:style>
    </ui:style>
    
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
            <g:FlowPanel>
                <g:FlowPanel styleName="formElementsPanel">
                    <g:DisclosurePanel ui:field="upGroupSelectionDisclosurePanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent"
                        open="true">
                        <g:header>
                            <ui:text from="{msg.getUsagePointAllGroupsTitle}" />
                        </g:header>
                        <g:ScrollPanel>
                            <g:VerticalPanel>
                            <g:FlowPanel>
                                <g:HorizontalPanel debugId="groupTypesPanel" ui:field="groupTypesPanel"></g:HorizontalPanel>
                                <p3:Message ui:field="groupError" visible="false"></p3:Message>
                            </g:FlowPanel>
                            <g:FlowPanel>
                                <form:FormRowPanel>
                                    <form:FormElement ui:field="suggestBoxExistingMeterElement" labelText="{msg.getUseExistingMeterInstructions}:" required="true" helpMsg="{msg.getMeterNumberSuggestionHelp}">
                                        <g:SuggestBox  ui:field="suggestBoxExistingMeterNumber" styleName="gwt-TextBox" tabIndex="1" debugId="suggestBoxExistingMeterNumber"/>
                                    </form:FormElement>
                                </form:FormRowPanel>
                            </g:FlowPanel>
                            </g:VerticalPanel>
                        </g:ScrollPanel>
                    </g:DisclosurePanel>
                </g:FlowPanel>       
                    
                <g:HorizontalPanel spacing="3" styleName="mainButtons" >
                    <g:Button  ui:field="selectBtn" text="" visible="false" debugId="selectBtn"/>
                    <g:Button  ui:field="clearBtn" text="{msg.getClearGroupsButton}" visible="false" debugId="clearBtn"/>
                </g:HorizontalPanel>
                    
           </g:FlowPanel>

</ui:UiBinder> 
