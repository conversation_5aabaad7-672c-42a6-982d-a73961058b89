package za.co.ipay.metermng.client.view.component.pricingstructure;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.UsagePointComponent;
import za.co.ipay.metermng.shared.dto.PSDto;

public class UpComponentValidatePStoMM extends AbstractValidatePStoMM {
    
    private UsagePointComponent parent;

    public UpComponentValidatePStoMM(UsagePointComponent parent) {
        super();
        this.parent = parent;
    }

    public void isregReadPsSameBillingDetsAsMeterModel(ClientFactory clientFactory, List<PSDto> pricingStructureDtos,
            Long mdcId, String userName, Logger logger) {
        super.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pricingStructureDtos, mdcId, userName, logger, "UsagePointComponent");
    }
    
    @Override
    public void errorNoTariff() {
        parent.resetBothPricingStructureLookupListBoxSelections();
        parent.toggleSaveBtns(true);
    }

    @Override
    public void noneMatchContinue() {
        parent.resetBothPricingStructureLookupListBoxSelections();
        parent.toggleSaveBtns(true);
    }

    @Override
    public void partialMatchConfirmContinue() {
        parent.saveContinue();
    }

    @Override
    public void partialMatchDenyContinue() {
        parent.resetBothPricingStructureLookupListBoxSelections();
        parent.toggleSaveBtns(true);
        return;
    }

    @Override
    public void exactMatchOrNoDataContinue() {
        parent.saveContinue();
    }

}
