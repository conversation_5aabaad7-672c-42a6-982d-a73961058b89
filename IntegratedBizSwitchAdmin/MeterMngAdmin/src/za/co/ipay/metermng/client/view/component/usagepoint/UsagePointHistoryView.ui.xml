<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client" 
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:component="urn:import:za.co.ipay.metermng.client.view.component">
	<ui:style>
		.cellTable {
	      border-bottom: 1px solid #ccc;
	      text-align: left;
	      margin-bottom: 4px;
	    }
		 
	</ui:style>
  
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
   
  <g:FlowPanel>
	<g:VerticalPanel>
	    <g:HTML ui:field="dataName" text="Data Name" styleName="dataTitle" />
        <g:HTML ui:field="dataDescription" text="Data Description" styleName="dataDescription" />
		<g:HorizontalPanel spacing="3">
			<g:Cell verticalAlignment="ALIGN_MIDDLE">
				<g:Label text="{msg.getUsagePointHistoryFilter}:" styleName="gwt-Label-bold"/>
			</g:Cell>
			<g:Cell verticalAlignment="ALIGN_MIDDLE">
				<g:ListBox visibleItemCount="1" name="Filter" ui:field="filterDropdown"/>
			</g:Cell>
			<g:Cell verticalAlignment="ALIGN_MIDDLE">
				<g:TextBox ui:field="txtbxfilter"/>
			</g:Cell>
            <component:DateRangeFilterPanel ui:field="dateFilter"/>
		</g:HorizontalPanel>
		          <c:CellTable ui:field="clltblHistory"/>
		          <p3:TablePager styleName="pager" ui:field="smplpgrHistory" location="CENTER" />
		        
	</g:VerticalPanel>
	</g:FlowPanel>
</ui:UiBinder> 