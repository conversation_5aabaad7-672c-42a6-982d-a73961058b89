package za.co.ipay.metermng.client.view.component.usagepoint;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TabLayoutPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.MultipleViewsFormPanel;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;

import java.util.logging.Logger;

public class UsagePointUnitsAccountTransactionViews extends BaseComponent {

    public static final int REPORT_TAB_HEIGHT = 900;
    public static final int GRAPH_TAB_HEIGHT = 550;

    @UiField MultipleViewsFormPanel multipleViewsForm;

    private UsagePointUnitsAccountTransactionChart graphPanel;
    private UsagePointUnitsAccountTransactionView tablePanel;
    private int tabLayoutPanelWidth;
    private int tabLayoutPanelHeight;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private boolean viewConstructed = false;
    private static Logger logger = Logger.getLogger(UsagePointUnitsAccountTransactionViews.class.getName());
    private static UsagePointUnitsAccountTransactionUiBinder uiBinder = GWT.create(UsagePointUnitsAccountTransactionUiBinder.class);
    private Long unitsAccountId;

    interface UsagePointUnitsAccountTransactionUiBinder extends UiBinder<Widget, UsagePointUnitsAccountTransactionViews> {
    }

    public UsagePointUnitsAccountTransactionViews(UsagePointWorkspaceView usagePointWorkspaceView,
            ClientFactory clientFactory, Long unitsAccountId) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.unitsAccountId = unitsAccountId;
        initWidget(uiBinder.createAndBindUi(this));
        resizeTabPanel();
        initViews();

        logger.info("Created UsagePointUnitsAccountTransactionViews");
    }

    public void resizeTabPanel() {

        tabLayoutPanelWidth = Window.getClientWidth() - (Window.getClientWidth() / 5);
        tabLayoutPanelHeight = REPORT_TAB_HEIGHT;
        multipleViewsForm.getTabLayoutPanel().setWidth(tabLayoutPanelWidth + "px");
        multipleViewsForm.getTabLayoutPanel().setHeight(tabLayoutPanelHeight + "px");
    }

    private void initViews() {
        initHeader();
        initForm();
        initGraphUi();
        initReportUi();

        initHandlers();
        multipleViewsForm.getTabLayoutPanel().selectTab(1, true);
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initHandlers() {
        final TabLayoutPanel panel = multipleViewsForm.getTabLayoutPanel();
        panel.addSelectionHandler(new SelectionHandler<Integer>() {
            @Override
            public void onSelection(SelectionEvent<Integer> event) {
                if (event.getSelectedItem().equals(0)) {
                    panel.setHeight(GRAPH_TAB_HEIGHT + "px");
                } else if (event.getSelectedItem().equals(1)) {
                    panel.setHeight(REPORT_TAB_HEIGHT + "px");
                }
            }
        });
    }

    private void initHeader() {
        multipleViewsForm.removeHeader();
    }

    private void initForm() {
        multipleViewsForm.getForm().removeFromParent();
    }

    private void initGraphUi() {
        graphPanel = new UsagePointUnitsAccountTransactionChart(clientFactory, getTabLayoutPanelWidth(),
                getTabLayoutPanelHeight());
        ScrollPanel scroll = new ScrollPanel(graphPanel);
        scroll.addStyleName("multipleView");
        multipleViewsForm.getTabLayoutPanel().add(scroll,
                new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.graph")));
    }

    private void initReportUi() {
        tablePanel = new UsagePointUnitsAccountTransactionView(usagePointWorkspaceView, clientFactory, unitsAccountId);
        multipleViewsForm.getTabLayoutPanel().add(tablePanel,
                new Label(MessagesUtil.getInstance().getMessage("meterreadings.header.table")));
    }

    public UsagePointUnitsAccountTransactionView getTablePanel() {
        return tablePanel;
    }

    public int getTabLayoutPanelWidth() {
        return tabLayoutPanelWidth;
    }

    public int getTabLayoutPanelHeight() {
        return tabLayoutPanelHeight;
    }

    public UsagePointUnitsAccountTransactionChart getGraphPanel() {
        return graphPanel;
    }
}
