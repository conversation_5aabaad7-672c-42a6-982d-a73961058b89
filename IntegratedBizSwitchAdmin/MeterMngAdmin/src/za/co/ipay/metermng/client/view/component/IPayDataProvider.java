package za.co.ipay.metermng.client.view.component;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.ListDataProvider;

public class IPayDataProvider<T> extends ListDataProvider<T> {
    
    private String filterString;  
    
    public final IpayDataProviderFilter<T> historyFilter;  
  
    public IPayDataProvider(IpayDataProviderFilter<T> filter) {  
        this.historyFilter = filter;  
    }  
  
    public String getFilter() {  
        return filterString;  
    }  
  
    public void setFilter(String filter) {  
        this.filterString = filter;  
        refresh();  
    }  
  
    public void resetFilter() {  
        filterString = null;  
        refresh();  
    }  
  
    public boolean hasFilter() {  
        return (filterString != null && !filterString.isEmpty());
    }  
  
    @Override  
    protected void updateRowData(HasData<T> display, int start, List<T> values) {
        int size = values.size();
        if (!hasFilter() || historyFilter == null) { // we don't need to filter, so call base class  
            display.setRowCount(size);
            super.updateRowData(display, start, values);  
        } else {  
            List<T> resulted = new ArrayList<T>();  
            
            for (int i = start; i < size; i++) {  
                if (historyFilter.isValid(values.get(i), getFilter())) {  
                    resulted.add(values.get(i));  
                }  
            }  
            display.setRowData(start, resulted);
            display.setRowCount(resulted.size());  
            
        }  
    }  
    
    public List<T> getFilteredList() {
        String activeFilter = this.getFilter();
        if (activeFilter == null || activeFilter.trim().isEmpty()) {
            // No filter => just return everything
            return getList();
        } else {
            // Create a new list containing only matching items
            List<T> filtered = new ArrayList<T>();
            for (T item : getList()) {
                if (item != null && historyFilter.isValid(item, activeFilter)) {
                    filtered.add(item);
                }
            }
            return filtered;
        }
    }
}  