<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
    xmlns:g="urn:import:com.google.gwt.user.client.ui"
    xmlns:c="urn:import:com.google.gwt.user.cellview.client"
    xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:form="urn:import:za.co.ipay.gwt.common.client.form">
    
    <ui:style>
    </ui:style>
    
   <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
   
                <g:FlowPanel ui:field="actionParamsView" >
                  <g:FlowPanel styleName="formElementsPanel">
                    <g:DisclosurePanel ui:field="actionParamsDisclosurePanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent" open="true">
                        <g:header>
                            <ui:text from="{msg.getActionParamsHeader}" />
                        </g:header>
                      
                      <g:VerticalPanel>
                          <g:HTMLPanel>
                             <g:VerticalPanel spacing="10" styleName="formElementsPanel" ui:field="actionParamsVerticalPanel" />
                          </g:HTMLPanel>
                          
                          <g:HTMLPanel ui:field="buttons" styleName="mainButtons">
                              <g:Button ui:field="submitBtn" text="{msg.getSubmitButton}"/>
                              <g:Button ui:field="closeBtn"  text="{msg.getCloseButton}"/>
                          </g:HTMLPanel>
                      </g:VerticalPanel>    
 
                    </g:DisclosurePanel>
                  </g:FlowPanel>
               </g:FlowPanel>
    
</ui:UiBinder> 