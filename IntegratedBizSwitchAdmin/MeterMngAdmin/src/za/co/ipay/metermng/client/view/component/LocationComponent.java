package za.co.ipay.metermng.client.view.component;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.GPSCoordinateBox;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.shared.validation.ClientEnhancedValidator;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.FormFields;
import za.co.ipay.metermng.mybatis.generated.model.Location;
import za.co.ipay.metermng.shared.dto.LocationData;

public class LocationComponent extends BaseComponent {

	interface LocationComponentUiBinder extends UiBinder<Widget, LocationComponent> {
	}
	private static LocationComponentUiBinder uiBinder = GWT.create(LocationComponentUiBinder.class);

	private LocationData location;
	protected HasDirtyData hasDirtyData;
    boolean mustSelectLastLevel = false;

	@UiField TextBox txtbxErfNumber;
	@UiField TextBox txtbxStreetNumber;
	@UiField TextBox txtbxBuildingName;
	@UiField TextBox txtbxSuiteNumber;
	@UiField TextBox txtbxAddress1;
	@UiField TextBox txtbxAddress2;
	@UiField TextBox txtbxAddress3;
	@UiField GPSCoordinateBox txtbxLatitude;
	@UiField GPSCoordinateBox txtbxLongitude;

	@UiField FormElement erfnumberElement;
	@UiField FormElement streetnumberElement;
	@UiField FormElement buildingNameElement;
	@UiField FormElement suiteNumberElement;
	@UiField FormElement addressElement;
	@UiField FormElement latitudeElement;
	@UiField FormElement longitudeElement;
    @UiField HorizontalPanel address1Panel;
    @UiField HorizontalPanel address2Panel;
    @UiField HorizontalPanel address3Panel;
	@UiField Label address1RequiredLabel;
	@UiField Label address2RequiredLabel;
	@UiField Label address3RequiredLabel;
    @UiField Label address1ErrorLabel;
    @UiField Label address2ErrorLabel;
    @UiField Label address3ErrorLabel;
    @UiField FormRowPanel erfNumberStreetNumberPanel;
    @UiField FormRowPanel buildingNameSuiteNumberPanel;
    @UiField FormGroupPanel addressPanel;
    @UiField FormRowPanel latitudeLongitudePanel;
    @UiField(provided=true) LocationGroupComponent locationGroupComponent;

	protected Workspace containerWorkspace = null;

    public LocationComponent() {
        initWidget(uiBinder.createAndBindUi(this));
    }

    public LocationComponent(ClientFactory clientFactory, HasDirtyData hasDirtyData) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = hasDirtyData;
        this.locationGroupComponent = new LocationGroupComponent(clientFactory, hasDirtyData);
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlers();
    }

    public void setLocation(LocationData theLocation) {
        this.location = theLocation;
        clearFields();
        mapDataToForm();
    }

    public LocationData getLocation() {
        return location;
    }

    public void setContainerWorkspace(Workspace workspace) {
        this.containerWorkspace = workspace;
    }

    public void mapDataToForm() {
        if (location != null) {
            txtbxErfNumber.setText(location.getErfNumber());
            txtbxStreetNumber.setText(location.getStreetNum());
            txtbxBuildingName.setText(location.getBuildingName());
            txtbxSuiteNumber.setText(location.getSuiteNum());
            txtbxAddress1.setText(location.getAddressLine1());
            txtbxAddress2.setText(location.getAddressLine2());
            txtbxAddress3.setText(location.getAddressLine3());

            if (location.getLatitude() != null) {
                txtbxLatitude.setText(location.getLatitude().toString());
            }
            if (location.getLongitude() != null) {
                txtbxLongitude.setText(location.getLongitude().toString());
            }
        } else {
            clearFields();
        }
        locationGroupComponent.setLocation(location);
    }

    public void mapFormToData() {
        if (location == null) {
            location = new LocationData();
        }
        if (txtbxErfNumber.getText().isEmpty()) {
            location.setErfNumber(null);
        } else {
            location.setErfNumber(txtbxErfNumber.getText());
        }

        if (txtbxStreetNumber.getText().isEmpty()) {
            location.setStreetNum(null);
        } else {
            location.setStreetNum(txtbxStreetNumber.getText());
        }

        if (txtbxBuildingName.getText().isEmpty()) {
            location.setBuildingName(null);
        } else {
            location.setBuildingName(txtbxBuildingName.getText());
        }

        if (txtbxSuiteNumber.getText().isEmpty()) {
            location.setSuiteNum(null);
        } else {
            location.setSuiteNum(txtbxSuiteNumber.getText());
        }

        if (txtbxAddress1.getText().isEmpty()) {
            location.setAddressLine1(null);
        } else {
            location.setAddressLine1(txtbxAddress1.getText());
        }

        if (txtbxAddress2.getText().isEmpty()) {
            location.setAddressLine2(null);
        } else {
            location.setAddressLine2(txtbxAddress2.getText());
        }

        if (txtbxAddress3.getText().isEmpty()) {
            location.setAddressLine3(null);
        } else {
            location.setAddressLine3(txtbxAddress3.getText());
        }


        if (txtbxLatitude.getText().isEmpty()) {
            location.setLatitude(null);
        } else {
            location.setLatitude(Double.parseDouble(txtbxLatitude.getText()));
        }

        if (txtbxLongitude.getText().isEmpty()) {
            location.setLongitude(null);
        } else {
            location.setLongitude(Double.parseDouble(txtbxLongitude.getText()));
        }
        location.setLocGroupId(locationGroupComponent.getLocation().getLocGroupId());
        location.setRecordStatus(RecordStatus.ACT);
    }

    public void clearErrors() {
        erfnumberElement.clearErrorMsg();
        streetnumberElement.clearErrorMsg();
        buildingNameElement.clearErrorMsg();
        suiteNumberElement.clearErrorMsg();
        addressElement.clearErrorMsg();

        latitudeElement.clearErrorMsg();
        longitudeElement.clearErrorMsg();

        address1ErrorLabel.setVisible(false);
        address1ErrorLabel.setText(null);
        address2ErrorLabel.setVisible(false);
        address2ErrorLabel.setText(null);
        address3ErrorLabel.setVisible(false);
        address3ErrorLabel.setText(null);
    }

    public void clearFields() {
        txtbxErfNumber.setText("");
        txtbxStreetNumber.setText("");
        txtbxBuildingName.setText("");
        txtbxSuiteNumber.setText("");
        txtbxAddress1.setText("");
        txtbxAddress2.setText("");
        txtbxAddress3.setText("");
        txtbxLatitude.setText("");
        txtbxLongitude.setText("");
    }

    public boolean validate() {
        boolean validated = true;

        clearErrors();

        Location loc = new Location();
        loc.setErfNumber(txtbxErfNumber.getText());
        loc.setStreetNum(txtbxStreetNumber.getText());
        loc.setBuildingName(txtbxBuildingName.getText());
        loc.setSuiteNum(txtbxSuiteNumber.getText());
        loc.setAddressLine1(txtbxAddress1.getText());
        loc.setAddressLine2(txtbxAddress2.getText());
        loc.setAddressLine3(txtbxAddress3.getText());

        ClientEnhancedValidator clientValidator = ClientValidatorUtil.getInstance();
        if (erfnumberElement.isVisible() && !clientValidator.validateField(loc, "erfNumber", erfnumberElement)) {
            validated = false;
        }
        if (streetnumberElement.isVisible() && !clientValidator.validateField(loc, "streetNum", streetnumberElement)) {
            validated = false;
        }
        if (buildingNameElement.isVisible()
                && !clientValidator.validateField(loc, "buildingName", buildingNameElement)) {
            validated = false;
        }
        if (suiteNumberElement.isVisible() && !clientValidator.validateField(loc, "suiteNum", suiteNumberElement)) {
            validated = false;
        }
        if (address1Panel.isVisible() && !clientValidator.validateField(loc, "addressLine1", addressElement)) {
            validated = false;
        }
        if (address2Panel.isVisible() && !clientValidator.validateField(loc, "addressLine2", addressElement)) {
            validated = false;
        }
        if (address3Panel.isVisible() && !clientValidator.validateField(loc, "addressLine3", addressElement)) {
            validated = false;
        }

        if (latitudeElement.isVisible() && !txtbxLatitude.getText().trim().isEmpty()) {
            if (!ValidateUtil.isValidLatitudeLongitude(txtbxLatitude.getText())) {
                latitudeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("location.latitude.invalid"));
                validated = false;
            }
        }
        if (longitudeElement.isVisible() && !txtbxLongitude.getText().trim().isEmpty()) {
            if (!ValidateUtil.isValidLatitudeLongitude(txtbxLongitude.getText())) {
                longitudeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("location.longitude.invalid"));
                validated = false;
            }
        }

		if (!locationGroupComponent.validate()) {
			validated = false;
		}

        return validated;
    }

    private void addFieldHandlers() {
        txtbxErfNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxStreetNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxBuildingName.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxSuiteNumber.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxAddress1.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxAddress2.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxAddress3.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxLatitude.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxLongitude.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    public void remapLocation() {
        mapDataToForm();
        locationGroupComponent.remapLocation();
    }

    public void setUserInterfaceComponentPreferences(FormFields fieldSetting, HorizontalPanel panel,
            Label requiredLabel, Label errorLabel) {
        errorLabel.setVisible(false);
        errorLabel.setText(null);
        panel.setVisible(fieldSetting.isDisplay());
        requiredLabel.setVisible(fieldSetting.isRequired());
    }

    public boolean handleUserInterfaceComponentGroups() {
        boolean erfNumberStreetNumberPanelVisible = erfnumberElement.isVisible() || streetnumberElement.isVisible();
        erfNumberStreetNumberPanel.setVisible(erfNumberStreetNumberPanelVisible);
        boolean buildingNameSuiteNumberPanelVisible = buildingNameElement.isVisible() || suiteNumberElement.isVisible();
        buildingNameSuiteNumberPanel.setVisible(buildingNameSuiteNumberPanelVisible);
        boolean addressPanelVisible = address1Panel.isVisible() || address2Panel.isVisible()
                || address3Panel.isVisible();
        addressPanel.setVisible(addressPanelVisible);
        boolean latitudeLongitudePanelVisible = latitudeElement.isVisible() || longitudeElement.isVisible();
        latitudeLongitudePanel.setVisible(latitudeLongitudePanelVisible);
        return erfNumberStreetNumberPanelVisible || buildingNameSuiteNumberPanelVisible || addressPanelVisible
                || latitudeLongitudePanelVisible;
    }

    boolean validateUserInterfaceComponent(HorizontalPanel panel, FormFields field, TextBox textBox, Label errorLabel) {
        if (panel.isVisible()) {
            String error = MeterMngClientUtils.getUserInterfaceComponentError(field, textBox.getValue());
            if (error != null) {
                errorLabel.setText(error);
                errorLabel.setVisible(true);
                return false;
            }
        }
        return true;
    }
}
