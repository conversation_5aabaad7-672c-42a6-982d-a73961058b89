package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.FileDetailPanel;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.SAPCustomerMovementDialogueBox;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class SAPCustomerMovementHelper extends BaseFiletypeHelper {

    public SAPCustomerMovementHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }

    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new SAPCustomerMovementDialogueBox(clientFactory, parent);
    }
    
    @Override
    public void setRegReadingsMsgVisible(FileDetailPanel fileDetailPanel) {
        fileDetailPanel.setRegReadReminderVisibility(true);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getCustomerMovementRecord().getMeterNumber();
    }

    @Override
    public String getUsagePointColumnValue(ImportFileItemDto object) {
        return object.getCustomerMovementRecord().getInstNo();
    }

    @Override
    public String getAgrRefColumnValue(ImportFileItemDto object) {
        return object.getCustomerMovementRecord().getContractAccount();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
        table.addColumn(tableColumnMap.get("usagePointCol"), messagesInstance.getMessage("import.up.label"));
        table.addColumn(tableColumnMap.get("agrRefCol"), messagesInstance.getMessage("import.agrref.label"));
    }
}
