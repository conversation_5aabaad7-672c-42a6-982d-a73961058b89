package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Date;
import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.FileDetailPanel;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.RegisterReadingImportDialogueBox;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class RegisterReadingImportHelper extends BaseFiletypeHelper {

    public RegisterReadingImportHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }

    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new RegisterReadingImportDialogueBox(clientFactory, parent);
    }
    
    @Override
    public void setRegReadingsMsgVisible(FileDetailPanel fileDetailPanel) {
        fileDetailPanel.setRegReadReminderVisibility(true);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getRegReadImportRecord().getMeterNum();
    }

    @Override
    public String getChannelValueColumnValue(ImportFileItemDto object) {
        return object.getRegReadImportRecord().getChannelValue();
    }

    @Override
    public String getReadingTimeStampColumnValue(ImportFileItemDto object) {
        Date timestamp = object.getRegReadImportRecord().getReadingTimestamp();
        if (timestamp != null) {
            return dfFormat.format(timestamp); 
        } else {
            return "Invalid";
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
        table.addColumn(tableColumnMap.get("channelValueCol"), messagesInstance.getMessage("import.reg.Read.channel.value.label"));
        table.addColumn(tableColumnMap.get("readingTimeStampCol"), messagesInstance.getMessage("import.reg.Read.timestamp.label"));
    }
}
