package za.co.ipay.metermng.client.view.component.usagepoint;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.datepicker.client.CalendarUtil;
import org.moxieapps.gwt.highcharts.client.Axis;
import org.moxieapps.gwt.highcharts.client.Chart;
import org.moxieapps.gwt.highcharts.client.Credits;
import org.moxieapps.gwt.highcharts.client.Legend;
import org.moxieapps.gwt.highcharts.client.Series;
import org.moxieapps.gwt.highcharts.client.labels.Labels;
import org.moxieapps.gwt.highcharts.client.labels.XAxisLabels;
import org.moxieapps.gwt.highcharts.client.plotOptions.BarPlotOptions;
import org.moxieapps.gwt.highcharts.client.plotOptions.ColumnPlotOptions;
import org.moxieapps.gwt.highcharts.client.plotOptions.PlotOptions;

import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class UsagePointUnitsAccountTransactionChart extends BaseComponent {

    public static final String CHART_DATE_STRING = "MMM y";

    private Long meterId = null;
    private FlowPanel panel;
    private ArrayList<UnitsTrans> trData = new ArrayList<>();
    private Chart chart;
    private int parentLayoutPanelWidth;
    private int parentLayoutPanelHeight;

    private static Logger log = Logger.getLogger(UsagePointUnitsAccountTransactionChart.class.getName());

    public UsagePointUnitsAccountTransactionChart(ClientFactory clientFactory, int parentLayoutPanelWidth,
            int parentLayoutPanelHeight) {
        this.clientFactory = clientFactory;
        this.parentLayoutPanelWidth = parentLayoutPanelWidth;
        this.parentLayoutPanelHeight = parentLayoutPanelHeight;
        panel = new FlowPanel();
        panel.setWidth("100%");
        panel.setHeight("500px");
        initWidget(panel);
    }

    public void populateTransactionData(ArrayList<UnitsTrans> transactions) {
        trData = transactions;
        clearChart();
        createChart();
    }

    private Map<String, Long> processTransactions() {
        Map<String, Long> processedTransData = new LinkedHashMap<>();
        Date date = new Date();
        date.setDate(1);
        CalendarUtil.addMonthsToDate(date, -12);

        for (int i = 0; i <= 12; i++) {
            processedTransData.put(DateTimeFormat.getFormat(CHART_DATE_STRING).format(date), 0L);
            CalendarUtil.addMonthsToDate(date, 1);
        }

        if (trData != null && !trData.isEmpty()) {
            for (UnitsTrans unitsTrans : trData) {
                String recordDateString = DateTimeFormat.getFormat(CHART_DATE_STRING).format(unitsTrans.getTransDate());
                if (processedTransData.containsKey(recordDateString)) {
                    processedTransData.put(recordDateString, processedTransData.get(recordDateString) + 1L);
                }
            }
        }

        return processedTransData;
    }

    private void createChart() {
        Map<String, Long> procData = processTransactions();
        chart = new Chart();
        // Remove credit at bottom right of chart
        chart.setCredits(new Credits().setEnabled(false));
        Messages messages = MessagesUtil.getInstance();
        chart.setType(Series.Type.COLUMN).setChartTitleText(messages.getMessage("transaction.history.graph.title"))
                .setChartSubtitleText(messages.getMessage("transaction.history.graph.description"))
                .setColumnPlotOptions(new ColumnPlotOptions().setStacking(PlotOptions.Stacking.NORMAL));
        ArrayList<String> xAxisArray = new ArrayList<>();
        List<Number> graphPoints = new ArrayList<>();

        for (String key : procData.keySet()) {
            xAxisArray.add(key);
            graphPoints.add(procData.get(key));
        }

        chart.getXAxis().setType(Axis.Type.DATE_TIME).setCategories(xAxisArray.toArray(new String[xAxisArray.size()]))
                .setAxisTitleText(messages.getMessage("transaction.history.graph.xaxis.label"))
                .setLabels(new XAxisLabels().setAlign(Labels.Align.CENTER).setEnabled(true));
        chart.getYAxis().setAxisTitleText(messages.getMessage("transaction.history.graph.yaxis.label")).setMin(0)
                .setAllowDecimals(false).setStartOnTick(false).setShowFirstLabel(false);
        chart.setLegend(new Legend().setEnabled(false));
        chart.addSeries(chart.createSeries().setName(messages.getMessage("transaction.history.graph.series.label"))
                .setPoints(graphPoints.toArray(new Number[graphPoints.size()]))
                .setPlotOptions(new BarPlotOptions().setShadow(false)));
        chart.setWidth(parentLayoutPanelWidth - 10 + "px");
        chart.setHeight(parentLayoutPanelHeight - 410 + "px");
        panel.add(chart);
    }

    protected void resizeChart() {
        if (chart != null) {
            chart.setSizeToMatchContainer();
            log.info("resized chart: " + chart.getOffsetHeight() + " " + chart.getOffsetWidth());
        }
    }

    private void clearChart() {
        if (chart != null) {
            panel.remove(chart);
            chart = null;
        }
    }

    public Long getMeterId() {
        return meterId;
    }

    public void setMeterId(Long meterId) {
        this.meterId = meterId;
    }
}