package za.co.ipay.metermng.client.view.component.usagepoint;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.MeterReadingsPlace;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.location.LocationHistoryView;
import za.co.ipay.metermng.client.view.component.mdc.MdcTransactionView;
import za.co.ipay.metermng.client.view.component.meter.RegisterReadingsView;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.view.workspace.meter.readings.view.MeterReadingsViews;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaData;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.shared.LocationHistData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.UsagePointHistData;
import za.co.ipay.metermng.shared.dto.LocationData;
import za.co.ipay.metermng.shared.dto.UpMeterInstallHistData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureHistData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.uploaddata.metadata.GisMetadata;

public class UsagePointInformation extends BaseComponent {

	private static MeterInformationUiBinder uiBinder = GWT.create(MeterInformationUiBinder.class);

	interface MeterInformationUiBinder extends UiBinder<Widget, UsagePointInformation> {
    }

	@UiField DisclosurePanel dsclsrUpTransPanel;
    @UiField VerticalPanel upTransPanelVP;

    @UiField DisclosurePanel dsclsrUsagePointHistory;
    @UiField VerticalPanel usagePointHistoryVP;

    @UiField DisclosurePanel dsclsrUpReportsSpecific;
    @UiField VerticalPanel upReportsSpecificVP;

    @UiField DisclosurePanel dsclsrMdcTransPanel;
    @UiField VerticalPanel mdcTransPanelVP;

    @UiField DisclosurePanel dsclsrUpUnitsAccountTransactionHistory;
    @UiField VerticalPanel unitsAccountTransactionHistoryVP;

    @UiField DisclosurePanel dsclsrUpMeterReadingsPanel;
    @UiField VerticalPanel upMeterReadingsVP;

    @UiField DisclosurePanel dsclsrUpRegisterReadingPanel;
    @UiField VerticalPanel upRegisterReadingVP;

    private UsagePointTransactionViews usagePointTransactions;
    private UsagePointHistoryView usagePointHistory;
    private LocationHistoryView locationHistory;
    private UsagePointInfoRecharge upInfoRecharge;
    private MdcTransactionView mdcTransactionView;
    private ClientFactory clientFactory;
    private UsagePointData usagePointData;
    private ArrayList<UsagePointHistData> upHistoryList;
    private ArrayList<CustomerTransAlphaDataWithTotals> upTransactionList;
    private ArrayList<CustomerTransAlphaData> upRechargeTransList;
    private ArrayList<LocationHistData> locationHistoryList;
    private ArrayList<MdcTransData> mdcTransactionList;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private ArrayList<AppSetting> customFieldList;
    private UsagePointUnitsAccountTransactionViews usagePointUnitsAccountTransactionViews;
    private UpMeterInstallHistoryView upMeterInstallHistoryView;
    private UpPricingStructureHistoryView upPricingStructureHistoryView;
    private MeterReadingsViews upMeterReadingsView;
    private RegisterReadingsView upRegisterReadingsView;

    public UsagePointInformation(ClientFactory clientFactory, ArrayList<AppSetting> customFieldList, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.clientFactory = clientFactory;
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.customFieldList = customFieldList;
        initWidget(uiBinder.createAndBindUi(this));
        init();
    }

    private void init() {
        this.dsclsrUpTransPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (usagePointTransactions == null || !usagePointTransactions.isViewConstructed()) {
                    usagePointTransactions = new UsagePointTransactionViews(clientFactory, usagePointWorkspaceView);
                    usagePointTransactions.setSize("100%", "550px");
                    upTransPanelVP.add(usagePointTransactions);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateUsagePointTransactions();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        this.dsclsrUsagePointHistory.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (usagePointHistory == null || !usagePointHistory.isViewConstructed() || locationHistory == null
                        || !locationHistory.isViewConstructed() || upMeterInstallHistoryView == null
                        || !upMeterInstallHistoryView.isViewConstructed()) {
                    usagePointHistory = new UsagePointHistoryView(customFieldList, clientFactory);

                    upPricingStructureHistoryView = new UpPricingStructureHistoryView(clientFactory);
                    upPricingStructureHistoryView.addStyleName("container");

                    upMeterInstallHistoryView = new UpMeterInstallHistoryView(clientFactory, false);
                    upMeterInstallHistoryView.addStyleName("container");

                    locationHistory = new LocationHistoryView();
                    locationHistory
                            .setHeadingText(MessagesUtil.getInstance().getMessage("location.history.physical.address"));
                    locationHistory.addStyleName("container");

                    usagePointHistoryVP.add(usagePointHistory);
                    usagePointHistoryVP.add(upPricingStructureHistoryView);
                    usagePointHistoryVP.add(upMeterInstallHistoryView);
                    usagePointHistoryVP.add(locationHistory);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateUsagePointHistory();
                        populateUpPricingStructureHistoryView();
                        populateUpMeterInstallHistData();
                        populatePhysicalLocationHistory();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        dsclsrUpReportsSpecific.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (upInfoRecharge == null || !upInfoRecharge.isViewConstructed()) {
                    upInfoRecharge = new UsagePointInfoRecharge(clientFactory);
                    upReportsSpecificVP.add(upInfoRecharge);
                }

                upInfoRecharge.setUsagePointInfo(usagePointData);
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateChrtUPRechargeTransactions(upInfoRecharge);
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                LocationData locationData = usagePointWorkspaceView.getUsagePointData().getServiceLocation();
                locationData = locationData != null ? locationData : usagePointWorkspaceView.getUsagePointData().getUsagepointLocation();
                upInfoRecharge.setMapCoordinates(locationData);
                upInfoRecharge.setGisMetadata(usagePointWorkspaceView.loadGisMetadata(usagePointData));
                upInfoRecharge.loadMapPanel();
                upInfoRecharge.setWidth("100%");      //must set width here not in constructor call above. SetWidth after have added map
            }
        });

        dsclsrMdcTransPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (mdcTransactionView == null || !mdcTransactionView.isViewConstructed()) {
                    mdcTransactionView = new MdcTransactionView(clientFactory);
                    mdcTransactionView.setWidth("100%");
                    mdcTransPanelVP.add(mdcTransactionView);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateMdcTrans();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        dsclsrUpUnitsAccountTransactionHistory.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                final Long unitsAccountId = usagePointData.getUnitsAccountId();
                if (usagePointUnitsAccountTransactionViews == null
                        || !usagePointUnitsAccountTransactionViews.isViewConstructed()) {
                    usagePointUnitsAccountTransactionViews = new UsagePointUnitsAccountTransactionViews(
                            usagePointWorkspaceView, clientFactory, unitsAccountId);
                    usagePointUnitsAccountTransactionViews.setSize("100%", "550px");
                    unitsAccountTransactionHistoryVP.add(usagePointUnitsAccountTransactionViews);
                }
                final UsagePointUnitsAccountTransactionView usagePointUnitsAccountTransactionView = usagePointUnitsAccountTransactionViews
                        .getTablePanel();
                usagePointUnitsAccountTransactionView.setUnitsAccountId(unitsAccountId);
                if (unitsAccountId != null) {
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            clientFactory.getUsagePointRpc().getUnitsAccountTransactions(unitsAccountId,
                                    new ClientCallback<ArrayList<UnitsTrans>>() {
                                        @Override
                                        public void onSuccess(ArrayList<UnitsTrans> result) {
                                            if (result != null) {
                                                usagePointUnitsAccountTransactionView.setUnitsAccountTransactionList(result);
                                                usagePointUnitsAccountTransactionViews.getGraphPanel()
                                                        .populateTransactionData(result);
                                            }
                                        }
                                    });
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        });

        dsclsrUpRegisterReadingPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (upRegisterReadingsView == null || !upRegisterReadingsView.isViewConstructed()) {
                    upRegisterReadingsView = new RegisterReadingsView(clientFactory, usagePointWorkspaceView);
                    upRegisterReadingsView.setWidth("100%");
                    upRegisterReadingVP.add(upRegisterReadingsView);
                }

                populateRegisterReadingsView();
            }
        });

        dsclsrUpMeterReadingsPanel.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (upMeterReadingsView == null || !upMeterReadingsView.isViewConstructed()) {
                    upMeterReadingsView = new MeterReadingsViews(clientFactory, new MeterReadingsPlace(MeterReadingsPlace.USAGE_POINT_METER, MessagesUtil.getInstance().getMessage("meterreadings.type.graph.single")), usagePointWorkspaceView);
                    upMeterReadingsView.setSize("100%", "600px");
                    upMeterReadingsVP.add(upMeterReadingsView);
                }
                upMeterReadingsView.setUsagePointData(usagePointData, usagePointData.getMeterData().getMeterModelData().getServiceResourceId());
            }
        });
    }

    private void populateRegisterReadingsView() {
        if (usagePointData != null) {
            dsclsrUpRegisterReadingPanel.setVisible(true);
            upRegisterReadingsView.setUsagePointData(usagePointData);
        } else {
            dsclsrUpRegisterReadingPanel.setVisible(false);
        }
    }

    public void populateUsagePointHistory() {
        if (usagePointData != null && usagePointData.getId() != null) {
            usagePointHistory.setVisible(true);
            clientFactory.getUsagePointRpc().fetchUsagePointHistory(usagePointData.getId(), new ClientCallback<ArrayList<UsagePointHistData>>() {
                @Override
                public void onSuccess(ArrayList<UsagePointHistData> result) {
                    if (result != null) {
                        upHistoryList = result;
                        usagePointHistory.setUsagePointHistoryList(upHistoryList);
                    }
                }
            });
        } else {
            usagePointHistory.setVisible(false);
        }
    }

    public void populatePhysicalLocationHistory() {
        if (usagePointData != null && usagePointData.getServiceLocationId() != null) {
            locationHistory.setVisible(true);
            clientFactory.getLocationRpc().getLocationHistory(usagePointData.getServiceLocationId(), new ClientCallback<ArrayList<LocationHistData>>() {
                @Override
                public void onSuccess(ArrayList<LocationHistData> result) {
                    if (result != null) {
                        locationHistoryList = result;
                        locationHistory.setLocationHistoryList(locationHistoryList);
                    }
                }
            });
        } else {
            locationHistory.setVisible(false);
        }
    }

    private void populateUpMeterInstallHistData() {
        if (usagePointData != null && usagePointData.getId() != null) {
            upMeterInstallHistoryView.setVisible(true);
            clientFactory.getUsagePointRpc().fetchUpMeterInstallHistory(usagePointData.getId(), null, clientFactory.isEnableAccessGroups(),
                    new ClientCallback<ArrayList<UpMeterInstallHistData>>() {
                        @Override
                        public void onSuccess(ArrayList<UpMeterInstallHistData> result) {
                            if (result != null) {
                                upMeterInstallHistoryView.setUpMeterInstallHistDataList(result);
                            }
                        }
                    });
        } else {
            upMeterInstallHistoryView.setVisible(false);
        }
    }

    private void populateUpPricingStructureHistoryView() {
        if (usagePointData != null && usagePointData.getId() != null) {
            upPricingStructureHistoryView.setVisible(true);
            clientFactory.getUsagePointRpc().fetchUpPricingStructureHistory(usagePointData.getId(),
                    (clientFactory.isEnableAccessGroups()),
                    new ClientCallback<List<UpPricingStructureHistData>>() {
                        @Override
                        public void onSuccess(List<UpPricingStructureHistData> result) {
                            if (result != null) {
                                upPricingStructureHistoryView.setUpPricingStructureHistDataList(result);
                            }
                        }
                    });
        } else {
            upPricingStructureHistoryView.setVisible(false);
        }
    }

    public void populateUsagePointTransactions() {
        if (usagePointTransactions != null) {
            Long usagePointId = usagePointData.getId();
            if (usagePointId != null) {
                usagePointTransactions.setVisible(true);
                clientFactory.getUsagePointRpc().fetchTransactionHistoryWithTotals(usagePointId, new ClientCallback<ArrayList<CustomerTransAlphaDataWithTotals>>() {
                    @Override
                    public void onSuccess(ArrayList<CustomerTransAlphaDataWithTotals> result) {
                        if (result != null) {
                            upTransactionList = result;
                            UsagePointTransactionView usagePointTransactionView = usagePointTransactions.getTablePanel();
                            usagePointTransactionView.setUsagePointTransactionList(upTransactionList);
                            usagePointTransactionView.setUsagePointData(usagePointData);
                            usagePointTransactions.getGraphPanel().populateTransactionData(upTransactionList);
                        }
                    }
                });
            } else {
                usagePointTransactions.setVisible(false);
            }
        }
    }

    public void populateChrtUPRechargeTransactions(final UsagePointInfoRecharge upInfoRecharge) {
        if (usagePointData.getId() != null) {
            clientFactory.getUsagePointRpc().fetchTransactionHistory(usagePointData.getId(), new ClientCallback<ArrayList<CustomerTransAlphaData>>() {
                @Override
                public void onSuccess(ArrayList<CustomerTransAlphaData> result) {
                    if (result != null) {
                        upRechargeTransList = result;
                        upInfoRecharge.chrtUpRecharge.populateTransactionData(upRechargeTransList);
                    }
                }
            });
        }
    }

    public void populateMdcTrans() {
        if (usagePointData != null) {
            dsclsrMdcTransPanel.setVisible(true);
            if (mdcTransactionView != null && mdcTransactionView.isViewConstructed()) {    //because UsagePointWorkspaceView notification handler
                mdcTransactionView.removeConnectDisconnectPanel();
                clientFactory.getUsagePointRpc().getMdcTransByUsagePoint(usagePointData.getId(), new ClientCallback<ArrayList<MdcTransData>>() {
                    @Override
                    public void onSuccess(ArrayList<MdcTransData> result) {
                        if (result != null) {
                            mdcTransactionList = result;
                            mdcTransactionView.setMdcTransactionList(mdcTransactionList);
                        }
                    }
                });
            }
        } else {
            dsclsrMdcTransPanel.setVisible(false);
        }
    }

    public UsagePointData getUsagePointData() {
        return usagePointData;
    }

    public void setUsagePointInfo(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
        if (usagePointData.getName() != null) {
            dsclsrUpReportsSpecific.getHeaderTextAccessor().setText(MessagesUtil.getInstance().getMessage("usagepoint.meter.reports") + " " + usagePointData.getName());
        }
        PricingStructure pricingStructure = usagePointData.getUpPricingStructureData().getPricingStructure();
        dsclsrUpUnitsAccountTransactionHistory.setVisible(!(usagePointData.getUnitsAccountId() == null
                || pricingStructure == null || pricingStructure.getPaymentModeId() != PaymentModeE.THIN_UNITS.getId()));
    }

    public void refreshUsagePointHistoryViewDeviceMoveRef(boolean refresh) {
        if (usagePointHistory != null && usagePointHistory.isViewConstructed()) {
            usagePointHistory.initTable(refresh);
        }
    }

    public void refreshUsagePointHistoryViewAppSettings() {
        if (usagePointHistory != null && usagePointHistory.isViewConstructed()) {
            usagePointHistory.refreshCustomAppSettings();
        }
    }

    public void upInfoRechargeSetMapCoords(LocationData locationData) {
        if (upInfoRecharge != null && upInfoRecharge.isViewConstructed()) {
            upInfoRecharge.setMapCoordinates(locationData);
        }
    }

    public void upInfoRechargeSetGisMetadata(List<GisMetadata> gisMetadata) {
        if (upInfoRecharge != null && upInfoRecharge.isViewConstructed()) {
            upInfoRecharge.setGisMetadata(gisMetadata);
        }
    }

    public void reloadMap() {
        if (upInfoRecharge != null && upInfoRecharge.isViewConstructed()) {
            upInfoRecharge.loadMapPanel();
        }
    }

    public void openTransactionHistory() {
        dsclsrUpTransPanel.setOpen(true);
    }

    public UsagePointTransactionViews getUsagePointTransactions() {
        return usagePointTransactions;
    }

    public void refreshUsagePointUnitsAccountTransactionViewTable() {
        if (usagePointUnitsAccountTransactionViews != null
                && usagePointUnitsAccountTransactionViews.isViewConstructed()) {
            usagePointUnitsAccountTransactionViews.getTablePanel().refreshUnitsAccountTransactionTable();
        }
    }
}
