package za.co.ipay.metermng.client.view.component.pricingstructure;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.AssignMeterDialogueBox;
import za.co.ipay.metermng.shared.dto.PSDto;

public class AssignMeterDialogueValidatePStoMM extends AbstractValidatePStoMM {
    
    private AssignMeterDialogueBox parent;

    public AssignMeterDialogueValidatePStoMM(AssignMeterDialogueBox parent) {
        super();
        this.parent = parent;
    }

    public void isregReadPsSameBillingDetsAsMeterModel(ClientFactory clientFactory, List<PSDto> pricingStructureDtos,
            Long mdcId, String userName, Logger logger) {
        super.isregReadPsSameBillingDetsAsMeterModel(clientFactory, pricingStructureDtos, mdcId, userName, logger, "AssignMeterDialogueBox");
    }
    
    @Override
    public void errorNoTariff() {
    }

    @Override
    public void noneMatchContinue() {
    }

    @Override
    public void partialMatchConfirmContinue() {
        parent.assignMeterCheckChannels();
    }

    @Override
    public void partialMatchDenyContinue() {
    }

    @Override
    public void exactMatchOrNoDataContinue() {
        parent.assignMeterCheckChannels();
    }

}
