package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.math.BigDecimal;
import java.util.Map;

import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.client.view.component.importfile.SolarDebtImportDialogueBox;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

public class SolarDebtImportHelper extends BaseFiletypeHelper {

    public SolarDebtImportHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }

    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new SolarDebtImportDialogueBox(clientFactory, parent);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getSolarDebtImportRecord().getAccountNumber();
    }

    @Override
    public String getUsagePointColumnValue(ImportFileItemDto object) {
        BigDecimal arrearsBal = object.getSolarDebtImportRecord().getArrearsBalance();
        if (arrearsBal != null) {
            return object.getSolarDebtImportRecord().getArrearsBalance().toPlainString();
        } else { 
            return "";
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.account.number"));
        table.addColumn(tableColumnMap.get("usagePointCol"), messagesInstance.getMessage("import.debtor.balance"));

    }
}
