<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component" 
	xmlns:p1="urn:import:com.google.gwt.user.datepicker.client"
	xmlns:ipay="urn:import:za.co.ipay.gwt.common.client.form"
    xmlns:p2="urn:import:za.co.ipay.gwt.common.client.widgets"
    xmlns:ipayusercustomfields="urn:import:za.co.ipay.metermng.client.view.component.usercustomfields">
	<ui:style>
		.rightalign {
          text-align: right;
        }       
	</ui:style>
	
  <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />
  
  <g:DisclosurePanel open="true" styleName="gwt-DisclosurePanel-component" ui:field="customerDisclosurePanel" debugId="customerDisclosurePanel">
        <g:customHeader>
            <g:FlowPanel styleName="gwt-DisclosurePanel-component .header" >
                <g:HorizontalPanel verticalAlignment="ALIGN_MIDDLE">
                        <g:Image styleName="horizontalFlow" ui:field="openorclosearrow"/>
                        <g:Image ui:field="customerImage" />
                        <g:Label debugId="customerDisclosurePanelHeader" styleName="gwt-DisclosurePanel-component .header" width="" ui:field="headerLabel" text="{msg.getCustomerTitle}:" horizontalAlignment="ALIGN_LEFT" />
                </g:HorizontalPanel>                
            </g:FlowPanel>
        </g:customHeader>
		<g:FlowPanel>
        <g:FlowPanel styleName="disclosureContent" ui:field="contentPanel">
        <g:FlowPanel styleName="formElementsPanel">
            <ipay:FormRowPanel width="100%"  styleName="{style.rightalign}" ui:field="assignLinkRow">
                <ipay:FormElement ui:field="assignLinkElement" helpMsg="{msg.getCustomerAssignHelp}">         
                    <g:Label debugId="assignCustomerLabel" text="{msg.getCustomerAssign}" ui:field="lblAssign" styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_RIGHT"/>
                </ipay:FormElement> 
            </ipay:FormRowPanel>				
            <g:FlowPanel ui:field="customerDetailsContainer">
                <ipay:FormRowPanel >
                     <ipay:FormElement debugId="titleElement" ui:field="custTitleElement" helpMsg="{msg.getCustomerFieldTitleHelp}" labelText="{msg.getCustomerFieldTitle}:">
                         <g:ListBox debugId="titleListBox" visibleItemCount="1" ui:field="titleListBox" styleName="gwt-ListBox-ipay" multipleSelect="false" />
                     </ipay:FormElement>
                     <ipay:FormElement debugId="initialsElement" ui:field="initialsElement" helpMsg="{msg.getCustomerInitialsHelp}" labelText="{msg.getCustomerInitials}:">
                        <g:TextBox debugId="initialsBox" ui:field="txtbxInitials" styleName="gwt-TextBox" visibleLength="3" />
                     </ipay:FormElement>
                     <ipay:FormElement ui:field="firstNamesElement" helpMsg="{msg.getCustomerFirstNamesHelp}" labelText="{msg.getCustomerFirstNames}:">
                        <g:TextBox debugId="nameBox"  ui:field="txtbxFirstNames" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="surnameElement" ui:field="surnameElement" helpMsg="{msg.getCustomerSurnameHelp}" labelText="{msg.getCustomerSurname}:" required="true">
                        <g:TextBox debugId="surnameBox" ui:field="txtbxSurname" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="idNumberElement" ui:field="idNumberElement" helpMsg="{msg.getCustomerIdNumberHelp}" labelText="{msg.getCustomerIdNumber}:">
                        <g:TextBox debugId="idNumberBox" ui:field="txtbxIdNumber" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                 </ipay:FormRowPanel>
                 <ipay:FormRowPanel ui:field="companyNameTaxNumberPanel">
                     <ipay:FormElement debugId="nameElement" ui:field="companyNameElement" helpMsg="{msg.getCustomerCompanyHelp}" labelText="{msg.getCustomerCompany}:">
                        <g:TextBox debugId="companyNameBox" ui:field="txtbxCompanyName" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="taxNumberElement" ui:field="taxNumberElement" helpMsg="{msg.getCustomerTaxHelp}" labelText="{msg.getCustomerTax}:">    
                        <g:TextBox debugId="taxNumberBox" ui:field="txtbxTax" styleName="gwt-TextBox" visibleLength="10"/>
                     </ipay:FormElement>
                 </ipay:FormRowPanel>
                 <ipay:FormRowPanel ui:field="contactDetailsPanel">
                     <ipay:FormElement debugId="emailAddressElement" ui:field="emailAddressElement" helpMsg="{msg.getCustomerEmailsHelp}" labelText="{msg.getCustomerEmail} 1:">
                         <g:TextBox debugId="emailBox" ui:field="txtbxEmail" styleName="gwt-TextBox gap" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="emailAddressElement2" ui:field="emailAddressElement2" helpMsg="{msg.getCustomerEmailsHelp}" labelText="{msg.getCustomerEmail} 2:">
                     	 <g:TextBox debugId="emailBox2" ui:field="txtbxEmail2" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="phoneNumberElement" ui:field="phoneNumberElement" helpMsg="{msg.getCustomerPhonesHelp}" labelText="{msg.getCustomerPhone1}:">
                          <g:TextBox debugId="phoneBox" ui:field="txtbxPhone" styleName="gwt-TextBox gap" visibleLength="15"/>
                     </ipay:FormElement>
                     <ipay:FormElement debugId="phoneNumberElement2" ui:field="phoneNumberElement2" helpMsg="{msg.getCustomerPhonesHelp}" labelText="{msg.getCustomerPhone2}:">
                          <g:TextBox debugId="phoneBox2" ui:field="txtbxPhone2" styleName="gwt-TextBox" visibleLength="15"/>
                     </ipay:FormElement>
                 </ipay:FormRowPanel>
            </g:FlowPanel>
            
            <ipay:FormRowPanel ui:field="customerRefPanel">
	            <ipay:FormElement debugId="customerRefElement" ui:field="customerRefElement" helpMsg="{msg.getCustomerReferenceHelp}" labelText="{msg.getCustomerReference}" required="true">
	                <g:TextBox debugId="txtbxCustRef" ui:field="txtbxCustRef" styleName="gwt-TextBox gap" visibleLength="15"/>
	            </ipay:FormElement>
            </ipay:FormRowPanel>
            
            <ipaycomp:MridComponent ui:field="mridComponent"></ipaycomp:MridComponent>
                                
            <ipayusercustomfields:UserCustomFieldsComponent ui:field="userCustomFieldsComponent" width="100%" visible="false"></ipayusercustomfields:UserCustomFieldsComponent>
            
            <ipay:FormGroupPanel labelText="{msg.getCustomerAgreement}" ui:field="customerAgreementPanel">
                   <ipay:FormRowPanel >
                       <ipay:FormElement debugId="agreementRefElement" ui:field="agreementRefElement" helpMsg="{msg.getCustomerAgreementRefHelp}" labelText="{msg.getCustomerAgreementRef}:" required="true">
                           <g:TextBox debugId="agreementRefBox" ui:field="txtbxAgreementRef" styleName="gwt-TextBox" visibleLength="15"/>
                       </ipay:FormElement>
                       <ipay:FormElement debugId="startDateElement" ui:field="startDateElement" helpMsg="{msg.getCustomerStartDateHelp}" labelText="{msg.getCustomerStartDate}:">
                          <p1:DateBox debugId="startDateBox" styleName="gwt-TextBox" ui:field="dtbxStart" />
                       </ipay:FormElement>
                       <ipay:FormElement debugId="freeIssueAuxAccountElement" ui:field="freeissueAuxAccountElement" helpMsg="{msg.getCustomerFreeIssueHelp}" labelText="{msg.getCustomerFreeIssue}:">
                           <p2:IpayListBox debugId="freeIssueAuxAccountBox" visibleItemCount="1" ui:field="lstbxFreeIssue" styleName="gwt-ListBox-ipay" multipleSelect="false"/>
                       </ipay:FormElement>
                       <ipay:FormElement debugId="billableElement" ui:field="billableElement" helpMsg="{msg.getBillableHelp}">
                           <g:CheckBox debugId="billableBox" text="{msg.getBillable}" checked="true" ui:field="chckbxBillable" />
                       </ipay:FormElement>
                   </ipay:FormRowPanel>                              
            </ipay:FormGroupPanel>
            <ipay:FormGroupPanel labelText="{msg.getCustomerAccount}" ui:field="customerAccountPanel">
                   <ipay:FormRowPanel > 
                       <ipay:FormElement debugId="accountNameElement" ui:field="accountNameElement" helpMsg="{msg.getCustomerAccountNameHelp}" labelText="{msg.getCustomerAccountName}:" required="true">
                           <g:TextBox debugId="accountNameBox" ui:field="txtbxAccountName" styleName="gwt-TextBox" visibleLength="15"/>
                       </ipay:FormElement>                   
                      <ipay:FormElement ui:field="accountBalanceElement" helpMsg="{msg.getCustomerAccountBalanceHelp}" labelText="{msg.getCustomerAccountBalance}">
                            <g:Label debugId="accountBalanceBox" text="0" ui:field="lblAccountBalance" styleName="accountBalance"/>
                       </ipay:FormElement>
                       <ipay:FormElement ui:field="btnSyncElement" helpMsg="{msg.getCustomerAccountSyncHelp}">
                            <g:Button debugId="btnSync" ui:field="btnSync" text="{msg.getCustomerAccountSyncButton}"/>
                       </ipay:FormElement>    
                   </ipay:FormRowPanel> 
                   <ipay:FormRowPanel ui:field="lowBalanceThresholdPanel">
                       <ipay:FormElement ui:field="lowBalanceThresholdElement" helpMsg="{msg.getCustomerAccountLowBalanceThresholdHelp}" labelText="{msg.getCustomerAccountLowBalanceThreshold}:" >
                           <p2:CurrencyTextBox debugId="lowBalanceThresholdBox" ui:field="txtbxlowThreshold" styleName="gwt-TextBox largeNumericInput" />
                       </ipay:FormElement>
                   </ipay:FormRowPanel> 
                   <ipay:FormRowPanel ui:field="notificationPanel">
                       <ipay:FormElement debugId="notificationEmailElement" ui:field="notificationEmailElement" helpMsg="{msg.getCustomerAccountNotificationEmailHelp}"
                       		labelText="{msg.getCustomerAccountNotificationEmail}:" >
                           <g:TextBox debugId="notificationEmailBox" ui:field="txtbxNotificationEmail" styleName="gwt-TextBox gap" visibleLength="25"/>
                       </ipay:FormElement>
                       <ipay:FormElement debugId="notificationPhoneElement" ui:field="notificationPhoneElement" helpMsg="{msg.getCustomerAccountNotificationPhoneHelp}" 
                       		labelText="{msg.getCustomerAccountNotificationPhone}:" >
                           <g:TextBox debugId="notificationPhoneBox" ui:field="txtbxNotificationPhone" styleName="gwt-TextBox gap" visibleLength="25"/>
                       </ipay:FormElement>
                   </ipay:FormRowPanel> 
                   <g:Label text="{msg.getCustomerAccountNote}"/>  
            </ipay:FormGroupPanel>
            <ipay:FormRowPanel width="100%"  ui:field="notifyLinkRow">
                <ipay:FormElement ui:field="notificationLinkElement">         
                  <g:Label debugId="notificationLabel" text="{msg.getCustomerManageNotificationTypes}" ui:field="lblNotification" styleName="gwt-Label-iPayLink" horizontalAlignment="ALIGN_LEFT"/>
                </ipay:FormElement>   
            </ipay:FormRowPanel>	
            <g:DisclosurePanel debugId="physicalAddressPanel" ui:field="physicalAddressPanel" width="100%" styleName="gwt-DisclosurePanel-insidecomponent">
                <g:header><ui:text from="{msg.getCustomerPhysicalAddress}" /></g:header>
                <g:FlowPanel styleName="">
                    <ipaycomp:LocationComponent debugId="physicalLocationComponent" ui:field="physicalLocationComponent" ></ipaycomp:LocationComponent>
                </g:FlowPanel>
            </g:DisclosurePanel>
        </g:FlowPanel>
            <g:FlowPanel ui:field="requiredKeys">
                <g:Label horizontalAlignment="ALIGN_RIGHT" styleName="requiredLabel" text="{msg.getCustomerRequired}" /> 
            </g:FlowPanel>
			<g:HorizontalPanel spacing="5" ui:field="buttons">
			     <g:Button debugId="customerSaveButton" ui:field="btnSave" text="{msg.getSaveButton}" />
			     <g:Button debugId="customerCancelButton" ui:field="btnCancel" text="{msg.getCancelButton}" />
			</g:HorizontalPanel>
		</g:FlowPanel>
		</g:FlowPanel>
	</g:DisclosurePanel>
</ui:UiBinder> 