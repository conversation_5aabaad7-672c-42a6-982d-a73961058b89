<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:p1="urn:import:za.co.ipay.gwt.common.client.form" xmlns:p2="urn:import:com.google.gwt.user.datepicker.client"
	xmlns:p3="urn:import:za.co.ipay.gwt.common.client.widgets" xmlns:ipaycomp="urn:import:za.co.ipay.metermng.client.view.component">

	<ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

                    <p1:FormGroupPanel ui:field="userCustomFieldsPanel" labelText="{msg.getUserCustomFieldsTitle}" visible="false" debugId="userCustomFieldsPanel">
                        <p1:FormRowPanel ui:field="customCharField1Panel" visible="false" debugId="customCharField1Panel">
                             <p1:FormElement ui:field="customCharField1Element" debugId="customCharField1Element">
                               <g:TextBox ui:field="customCharField1" styleName="gwt-TextBox" visibleLength="40" debugId="customCharField1"/>
                             </p1:FormElement>
                         </p1:FormRowPanel> 
                         <p1:FormRowPanel ui:field="customCharField2Panel" visible="false">
                             <p1:FormElement ui:field="customCharField2Element">
                               <g:TextBox ui:field="customCharField2" styleName="gwt-TextBox" visibleLength="40"/>
                             </p1:FormElement>
                         </p1:FormRowPanel> 
                         <p1:FormRowPanel ui:field="customNumPanel" visible="false" debugId="customNumPanel">
                             <p1:FormElement ui:field="customNumField1Element"  visible="false">
                               <p1:BigDecimalValueBox ui:field="customNumField1" styleName="gwt-TextBox-ipay" visibleLength="15" debugId="customNumField1"/>
                             </p1:FormElement>
                             <p1:FormElement ui:field="customNumField2Element"  visible="false">
                               <p1:BigDecimalValueBox ui:field="customNumField2" styleName="gwt-TextBox-ipay" visibleLength="15"/>
                             </p1:FormElement>
                         </p1:FormRowPanel>      
                         <p1:FormRowPanel ui:field="customDatePanel" visible="false">
                             <p1:FormElement ui:field="customDateField1Element"  visible="false">
                               <p2:DateBox ui:field="customDateField1" styleName="gwt-TextBox"/>
                             </p1:FormElement>
                             <p1:FormElement ui:field="customDateField2Element"  visible="false">
                               <p2:DateBox ui:field="customDateField2" styleName="gwt-TextBox"/>
                             </p1:FormElement>
                         </p1:FormRowPanel>      
                    </p1:FormGroupPanel>
 
</ui:UiBinder> 