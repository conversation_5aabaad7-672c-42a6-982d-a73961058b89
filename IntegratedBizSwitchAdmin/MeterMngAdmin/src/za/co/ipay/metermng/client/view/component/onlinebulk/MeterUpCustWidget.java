package za.co.ipay.metermng.client.view.component.onlinebulk;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.event.LinkToEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupDisplay.ContainsUPGroupDisplayPanel;
import za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupDisplay.UpGroupDisplayPanel;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;
import za.co.ipay.metermng.shared.dto.UpGenGroupLinkData;
import za.co.ipay.metermng.shared.dto.UpPricingStructureData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.util.MeterMngSharedUtils;

public class MeterUpCustWidget extends BaseComponent implements ContainsUPGroupDisplayPanel {

    @UiField FlowPanel upGroupPanel;
    @UiField CellTable<LabelValue> clltbltransitems;
    
    @UiField Label lblEngTokenLnk;
    @UiField Label lblCreditTokenLnk;

    private DialogBox simplePopup;
    private ListDataProvider<LabelValue> dataProvider = new ListDataProvider<LabelValue>();
    private ContainsMeterUpCustComponent parentWidget;
    private UsagePointData usagePointData;
    
    private static Logger logger = Logger.getLogger(MeterUpCustWidget.class.getName());
    
    private static MeterUpCustWidgetUiBinder uiBinder = GWT.create(MeterUpCustWidgetUiBinder.class);
    
    interface MeterUpCustWidgetUiBinder extends UiBinder<Widget, MeterUpCustWidget> {
    }

    public MeterUpCustWidget(ClientFactory clientfactory, ContainsMeterUpCustComponent parentWidget, UsagePointData usagePointData, int left, int top) {
        this.clientFactory = clientfactory;
        this.parentWidget = parentWidget;
        this.usagePointData = usagePointData;
        initWidget(uiBinder.createAndBindUi(this));
        
        setupTable();
        populatePopup(left, top);
    }
    
    protected void setupTable() {
         AbstractCell<String> labelCell = new AbstractCell<String>() { 
            @Override
            public void render(Context context, String value, SafeHtmlBuilder sb) {
                sb.appendHtmlConstant("<span class=\"formElement_label\">" + value + "</span>");
            }
        }; 
        Column<LabelValue, String> labelColumn = new Column<LabelValue, String>(labelCell) {
            @Override
            public String getValue(LabelValue data) {
                return data.getLbl();         
            }
        };
        
        TextColumn<LabelValue> valueColumn = new TextColumn<LabelValue>() {
            @Override
            public String getValue(LabelValue data) {
                return data.getValue();
            }
        };

        
        clltbltransitems.addColumn(labelColumn, MessagesUtil.getInstance().getMessage("popup.label"));
        clltbltransitems.addColumn(valueColumn, MessagesUtil.getInstance().getMessage("popup.value"));
         
        dataProvider.addDataDisplay(clltbltransitems);
    }
    
    public void populatePopup(final int left, final int top) {
        ArrayList<UpGenGroupLinkData> upGenGroupLinkDataList = new ArrayList<UpGenGroupLinkData>();
        upGenGroupLinkDataList.addAll(usagePointData.getUpgengroups().values());
        clientFactory.getGroupRpc().getUpGenGroupNamesList(upGenGroupLinkDataList, new ClientCallback<ArrayList<UpGenGroupLinkDataNames>>() {
            @Override
            public void onSuccess(ArrayList<UpGenGroupLinkDataNames> result) {
                buildFieldList(result, left, top);
            }
        });
    }
    
    private void buildFieldList(ArrayList<UpGenGroupLinkDataNames> upGroupNamesList, int left, int top) {
        upGroupPanel.add(new UpGroupDisplayPanel(upGroupNamesList, this));
        ArrayList<LabelValue> fieldList = new ArrayList<LabelValue>();
        
        if (usagePointData.getMeterData() != null) {
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("meter.number"), usagePointData.getMeterData().getMeterNum()));
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("meter.models.name"), usagePointData.getMeterData().getMeterModelData().getName()));
            if (usagePointData.getMeterData().getCurrStsSupplyGroup() != null) {
                fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("supplygroup.name"), usagePointData.getMeterData().getCurrStsSupplyGroup().getName()));
            }
            if (usagePointData.getMeterData().getStsMeter() == null) {
                lblEngTokenLnk.removeFromParent();
                lblCreditTokenLnk.removeFromParent();
            }
        } else {
            lblEngTokenLnk.removeFromParent();
        }
        if (usagePointData.getInstallationDate() != null) {
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate"), FormatUtil.getInstance().formatDateTime(usagePointData.getInstallationDate())));
        } else {
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("meter.online.bulk.installdate"), ""));
        }
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("usagepoint.field.pricingstructure"), usagePointData.getUpPricingStructureData().getPricingStructure().getName()));
        UpPricingStructureData futureData = usagePointData.getUpPricingStructureData().getFutureUpPricingStructureData();
        if (futureData != null && futureData.getPricingStructure() != null) {
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("usagepoint.ps.future.lbl"), futureData.getPricingStructure().getName()));
            fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("usagepoint.ps.future.start.date.lbl"), FormatUtil.getInstance().formatDateTime(futureData.getUpPricingStructure().getStartDate())));
        }
        
        String suiteNum = "";
        if (usagePointData.getServiceLocation() != null 
            && usagePointData.getServiceLocation().getSuiteNum() != null) {
            suiteNum = usagePointData.getServiceLocation().getSuiteNum();
            if (MeterMngSharedUtils.isNumber(suiteNum)) {
                suiteNum = MessagesUtil.getInstance().getMessage("online.bulk.panel.suite.no.text") + suiteNum;
            }
        }
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("location.field.suitenumber"), suiteNum));
        
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("usagepoint.field.name"), usagePointData.getName()));
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("meter.online.bulk.usagepoint.status"), getStatus(usagePointData.getRecordStatus())));

        String surname = "";
        String phoneNum = "";
        if (usagePointData.getCustomerAgreementData() != null && usagePointData.getCustomerAgreementData().getCustomerData() != null) {
            surname = usagePointData.getCustomerAgreementData().getCustomerData().getSurname();
            if (MeterMngSharedUtils.isNumber(surname)) {
                surname = MessagesUtil.getInstance().getMessage("online.bulk.panel.tenant.text") + surname;
            }
            if (usagePointData.getCustomerAgreementData().getCustomerData().getPhone1() != null) {
                phoneNum = usagePointData.getCustomerAgreementData().getCustomerData().getPhone1();
            }
        }
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("customer.surname"), surname));
        fieldList.add(new LabelValue(MessagesUtil.getInstance().getMessage("customer.phone"), phoneNum));
        
        // mapUserCustomFields();

        dataProvider.setList(fieldList);
        show(left, top);
    }
    
    @UiHandler("lblEngTokenLnk")
    void handleEngTokenLink(ClickEvent event) {
        //link is removed when no meterdata
        LinkToEvent linkToEvent = null;
        if (usagePointData.getMeterData() != null) {
            linkToEvent = new LinkToEvent(LinkToEvent.ENGINEERING_TOKEN_HISTORY, usagePointData.getMeterData().getMeterNum(), LinkToEvent.METER_NUM);
        } 
        clientFactory.getEventBus().fireEvent(linkToEvent);
    }

    @UiHandler("lblCreditTokenLnk")
    void handleCreditTokenLink(ClickEvent event) {
        LinkToEvent linkToEvent = null;
        if (usagePointData.getMeterData() != null) {
            linkToEvent = new LinkToEvent(LinkToEvent.CREDIT_TOKEN_HISTORY, usagePointData.getMeterData().getMeterNum(), LinkToEvent.METER_NUM);
        } else {
            linkToEvent = new LinkToEvent(LinkToEvent.CREDIT_TOKEN_HISTORY, usagePointData.getName(), LinkToEvent.USAGE_POINT_NAME);
        }
        clientFactory.getEventBus().fireEvent(linkToEvent);
    }
    
    public void show(int left, int top) {
    	simplePopup = new DialogBox(true);
    	simplePopup.setText(MessagesUtil.getInstance().getMessage("more.info"));
        simplePopup.setAnimationEnabled(true);
		simplePopup.setWidget(this);
    	simplePopup.setPopupPosition(left, top);
		simplePopup.show();
    }
    
    private class LabelValue {
        private String lbl;
        private String value; 
        
        public LabelValue(String lbl, String value) {
            this.lbl = lbl;
            if (value != null) {
                this.value = value;
            }
        }
        
        public String getLbl() {
            return lbl;
        }

        public String getValue() {
            return value;
        }

    }
    
    private String getStatus(RecordStatus status) {
        if (status.equals(RecordStatus.ACT)) {
            return MessagesUtil.getInstance().getMessage("status.active");
        } else if (status.equals(RecordStatus.DAC)) {
            return MessagesUtil.getInstance().getMessage("status.inactive");
        } else if (status.equals(RecordStatus.DEL)) {
            return MessagesUtil.getInstance().getMessage("status.deleted");
        } else {
            return status.toString();
        }
    }

    @Override
    public void setSelectedUpGroupTypes( ArrayList<Long> selectedUpGenGroupTypeIdsList, boolean copiedFromOtherMeter) {
        ArrayList<UpGenGroupLinkData> selectedUpGenGroupLinkDataList = new ArrayList<UpGenGroupLinkData>();
        for (Long typeId : selectedUpGenGroupTypeIdsList) {
            selectedUpGenGroupLinkDataList.add(usagePointData.getSelectedGroupByGroupType(typeId));
        }
        parentWidget.setSelectedGroups(selectedUpGenGroupLinkDataList, copiedFromOtherMeter);
        simplePopup.hide();
    }

    
}
