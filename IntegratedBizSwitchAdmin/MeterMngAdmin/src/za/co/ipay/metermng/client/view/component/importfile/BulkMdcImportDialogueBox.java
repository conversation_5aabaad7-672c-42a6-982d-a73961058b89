package za.co.ipay.metermng.client.view.component.importfile;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkmdc.BulkMdcImportRecord;
import za.co.ipay.metermng.shared.integration.bulkmdc.BulkMdcParseResult;

public class BulkMdcImportDialogueBox extends ImportFileItemBaseDialogueBox {

    private BulkMdcImportRecord recordIn;

    public BulkMdcImportDialogueBox(ClientFactory clientFactory, ImportFileItemView importFileItemView) {
        super(clientFactory, importFileItemView);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        BulkMdcImportRecord record = recordIn = itemDto.getBulkMdcImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Identifier Type", record.getIdentifierType()));
        dataList.add(new ImportRecordField("Identifier", record.getIdentifier()));
        dataList.add(new ImportRecordField("Request Type", record.getRequestType()));
        dataList.add(new ImportRecordField("Control Type", record.getControlType()));
        if (record.getParameters() != null) {
            dataList.add(new ImportRecordField("Parameters", convertArrToStr(record.getParameters())));
        } else {
            dataList.add(new ImportRecordField("Parameters", ""));
        }
        if (record.getOverride() != null) {
            dataList.add(new ImportRecordField("Override", record.getOverride()));
        } else {
            dataList.add(new ImportRecordField("Override", ""));
        }
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        isDirtyData = false;
        BulkMdcParseResult result = createRecordFromList();
        if (result.getErrorMsg() != null && !result.getErrorMsg().isEmpty()) {
            isDirtyData = true;
            return;
        }
        BulkMdcImportRecord chgRec = result.getBulkMdcImportRecord();
        if (!recordIn.getIdentifierType().equals(chgRec.getIdentifierType())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getIdentifier().equals(chgRec.getIdentifier())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getRequestType().equals(chgRec.getRequestType())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getControlType().equals(chgRec.getControlType())) {
            isDirtyData = true;
            return;
        }
        if (!recordIn.getOverride().equals(chgRec.getOverride())) {
            isDirtyData = true;
            return;
        }
        if ((recordIn.getParameters() == null && chgRec.getParameters() != null)
                || (recordIn.getParameters() != null && chgRec.getParameters() == null)
                || (recordIn.getParameters() != null && chgRec.getParameters() != null
                && !recordIn.getParameters().equals(chgRec.getParameters()))) {
            isDirtyData = true;
            return;
        }
    }

    private BulkMdcParseResult createRecordFromList() {
        StringBuilder errorBuilder = new StringBuilder();

        BulkMdcImportRecord chgRec = new BulkMdcImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Identifier Type")) {
                chgRec.setIdentifierType(field.getFieldValue());
            }
            if (field.getFieldname().equals("Identifier")) {
                chgRec.setIdentifier(field.getFieldValue());
            }
            if (field.getFieldname().equals("Control Type")) {
                chgRec.setControlType(field.getFieldValue());
            }
            if (field.getFieldname().equals("Request Type")) {
                chgRec.setRequestType(field.getFieldValue());
            }
            if (field.getFieldname().equals("Override")) {
                chgRec.setOverride(field.getFieldValue());
            }
            if (field.getFieldname().equals("Parameters")) {
                try {
                    String[] splitParams = field.getFieldValue().split("\\|");
                    List<String> paramList = new ArrayList<String>();
                    for (int i = 0; i < splitParams.length; i++) {
                        String param = splitParams[i];
                        if (param != null) {
                            param = param.trim();
                            if (param.length() > 0) {
                                paramList.add(param);
                            }
                        }
                    }
                    String[] paramsArray = paramList.toArray(new String[paramList.size()]);
                    chgRec.setParameters(paramsArray);
                } catch (Exception e) {
                    errorBuilder.append("Parameters must be pipe delimited. '|'");
                }
            }
        }

        String errors = null;
        if (errorBuilder.length() != 0) {
            errorBuilder.insert(0, "Errors: ");
            errors = errorBuilder.toString();
            errors = errors.substring(0, errors.length() - 2);
        }
        return (new BulkMdcParseResult(chgRec, errors));
    }

    private String convertArrToStr(String[] arr) {
        String str = "";
        if (arr != null) {
            str = Arrays.toString(arr).replaceAll(",", "|").replaceAll("\\[|\\]| ", "");
        }
        return str;
    }

    @Override
    protected void updateParentRow() {
        BulkMdcParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getBulkMdcImportRecord());
        // eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }

    @Override
    protected void displayUpdateMessage() {
        BulkMdcParseResult result = createRecordFromList();
        BulkMdcImportRecord record = result.getBulkMdcImportRecord();
        Dialogs.displayInformationMessage(MessagesUtil.getInstance()
                .getMessage("import.edit.bulk.mdc.item.update.success",
                        new String[] { record.getIdentifier() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        BulkMdcParseResult result = createRecordFromList();
        importFileItemDto.setGenericImportRecord(result.getBulkMdcImportRecord());
        // eventual update in DefaultImportService is selective, so null won't update
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment(result.getErrorMsg());
    }
}
