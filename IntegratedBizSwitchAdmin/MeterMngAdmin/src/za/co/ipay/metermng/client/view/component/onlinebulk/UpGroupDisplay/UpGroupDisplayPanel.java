package za.co.ipay.metermng.client.view.component.onlinebulk.UpGroupDisplay;

import java.util.ArrayList;

import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.UpGenGroupLinkDataNames;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ProvidesResize;
import com.google.gwt.user.client.ui.RequiresResize;
import com.google.gwt.user.client.ui.Widget;

public class UpGroupDisplayPanel extends BaseComponent implements ProvidesResize, RequiresResize {

    @UiField HorizontalPanel panel;
    @UiField Label lblCopyGroups;
    private ContainsUPGroupDisplayPanel parentPanel;
    
    private static UpGroupDisplayPanelUiBinder uiBinder = GWT.create(UpGroupDisplayPanelUiBinder.class);
    interface UpGroupDisplayPanelUiBinder extends UiBinder<Widget, UpGroupDisplayPanel> {
    }
    
    public UpGroupDisplayPanel(ArrayList<UpGenGroupLinkDataNames> upGroupNamesList, ContainsUPGroupDisplayPanel containerPanel) {
        initWidget(uiBinder.createAndBindUi(this));
        this.parentPanel = containerPanel;
        if (upGroupNamesList.isEmpty()) {
            panel.add(new Label(MessagesUtil.getInstance().getMessage("meter.not.in.groups")));
        } else {
            for (UpGenGroupLinkDataNames upGroupNames : upGroupNamesList) {
                panel.add(new SingleUpGroupDisplayWidget(upGroupNames));
            }
        }
    }
    
    @UiHandler("lblCopyGroups")
    public void handleCopyLink(ClickEvent event) {
        SingleUpGroupDisplayWidget singleUpGroupDisplayWidget;
        ArrayList<Long> selectedUpGenGroupTypeIdsList = new ArrayList<Long>();
        for (int i = 0; i<panel.getWidgetCount(); i++ ) {
            if (panel.getWidget(i) instanceof SingleUpGroupDisplayWidget) {
                singleUpGroupDisplayWidget = (SingleUpGroupDisplayWidget)panel.getWidget(i);
                if (singleUpGroupDisplayWidget.isSelected()) {
                    selectedUpGenGroupTypeIdsList.add(singleUpGroupDisplayWidget.getGroupTypeId());
                }
            }
        }
        parentPanel.setSelectedUpGroupTypes(selectedUpGenGroupTypeIdsList, true);
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                panel.setWidth("100%");
            }
        }.schedule(100);

    }
    
    
}
